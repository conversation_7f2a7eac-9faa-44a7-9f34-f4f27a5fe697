namespace MassStorageStableTestTool.Core.Exceptions;

/// <summary>
/// 测试工具未找到异常
/// </summary>
public class TestToolNotFoundException : Exception
{
    /// <summary>
    /// 工具名称
    /// </summary>
    public string ToolName { get; }

    /// <summary>
    /// 工具路径
    /// </summary>
    public string? ToolPath { get; }

    public TestToolNotFoundException(string toolName) 
        : base($"测试工具 '{toolName}' 未找到")
    {
        ToolName = toolName;
    }

    public TestToolNotFoundException(string toolName, string toolPath) 
        : base($"测试工具 '{toolName}' 在路径 '{toolPath}' 未找到")
    {
        ToolName = toolName;
        ToolPath = toolPath;
    }

    public TestToolNotFoundException(string toolName, Exception innerException) 
        : base($"测试工具 '{toolName}' 未找到", innerException)
    {
        ToolName = toolName;
    }

    public TestToolNotFoundException(string toolName, string toolPath, Exception innerException) 
        : base($"测试工具 '{toolName}' 在路径 '{toolPath}' 未找到", innerException)
    {
        ToolName = toolName;
        ToolPath = toolPath;
    }
}
