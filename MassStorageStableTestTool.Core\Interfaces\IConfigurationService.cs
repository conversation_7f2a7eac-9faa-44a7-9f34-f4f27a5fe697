using MassStorageStableTestTool.Core.Models;

namespace MassStorageStableTestTool.Core.Interfaces;

/// <summary>
/// 配置服务接口
/// </summary>
public interface IConfigurationService
{
    /// <summary>
    /// 加载配置
    /// </summary>
    /// <param name="configPath">配置文件路径</param>
    /// <returns>配置对象</returns>
    Task<ApplicationConfiguration> LoadConfigurationAsync(string? configPath = null);

    /// <summary>
    /// 保存配置
    /// </summary>
    /// <param name="configuration">配置对象</param>
    /// <param name="configPath">配置文件路径</param>
    /// <returns>保存结果</returns>
    Task<bool> SaveConfigurationAsync(ApplicationConfiguration configuration, string? configPath = null);

    /// <summary>
    /// 创建默认配置
    /// </summary>
    /// <returns>默认配置</returns>
    ApplicationConfiguration CreateDefaultConfiguration();

    /// <summary>
    /// 验证配置
    /// </summary>
    /// <param name="configuration">配置对象</param>
    /// <returns>验证结果</returns>
    (bool IsValid, List<string> Errors) ValidateConfiguration(ApplicationConfiguration configuration);

    /// <summary>
    /// 获取测试工具配置
    /// </summary>
    /// <param name="toolName">工具名称</param>
    /// <returns>工具配置</returns>
    Task<TestToolConfig?> GetToolConfigurationAsync(string toolName);

    /// <summary>
    /// 更新测试工具配置
    /// </summary>
    /// <param name="toolName">工具名称</param>
    /// <param name="config">工具配置</param>
    /// <returns>更新结果</returns>
    Task<bool> UpdateToolConfigurationAsync(string toolName, TestToolConfig config);

    /// <summary>
    /// 获取所有测试工具配置
    /// </summary>
    /// <returns>工具配置字典</returns>
    Task<Dictionary<string, TestToolConfig>> GetAllToolConfigurationsAsync();

    /// <summary>
    /// 导入配置
    /// </summary>
    /// <param name="filePath">配置文件路径</param>
    /// <param name="format">配置格式</param>
    /// <returns>导入的配置</returns>
    Task<ApplicationConfiguration> ImportConfigurationAsync(string filePath, string format = "json");

    /// <summary>
    /// 导出配置
    /// </summary>
    /// <param name="configuration">配置对象</param>
    /// <param name="filePath">导出文件路径</param>
    /// <param name="format">导出格式</param>
    /// <returns>导出结果</returns>
    Task<bool> ExportConfigurationAsync(ApplicationConfiguration configuration, string filePath, string format = "json");

    /// <summary>
    /// 重置配置为默认值
    /// </summary>
    /// <returns>重置结果</returns>
    Task<bool> ResetToDefaultAsync();

    /// <summary>
    /// 备份当前配置
    /// </summary>
    /// <param name="backupPath">备份路径</param>
    /// <returns>备份结果</returns>
    Task<bool> BackupConfigurationAsync(string? backupPath = null);

    /// <summary>
    /// 恢复配置
    /// </summary>
    /// <param name="backupPath">备份路径</param>
    /// <returns>恢复结果</returns>
    Task<bool> RestoreConfigurationAsync(string backupPath);

    /// <summary>
    /// 获取配置历史
    /// </summary>
    /// <param name="count">历史记录数量</param>
    /// <returns>配置历史列表</returns>
    Task<List<ConfigurationHistory>> GetConfigurationHistoryAsync(int count = 10);

    /// <summary>
    /// 配置变化事件
    /// </summary>
    event EventHandler<ConfigurationChangedEventArgs>? ConfigurationChanged;
}

/// <summary>
/// 应用程序配置模型
/// </summary>
public class ApplicationConfiguration
{
    /// <summary>
    /// 测试工具配置字典
    /// </summary>
    public Dictionary<string, TestToolConfig> TestTools { get; set; } = new();

    /// <summary>
    /// 报告配置
    /// </summary>
    public ReportConfiguration Reporting { get; set; } = new();

    /// <summary>
    /// 日志配置
    /// </summary>
    public LoggingConfiguration Logging { get; set; } = new();

    /// <summary>
    /// 性能配置
    /// </summary>
    public PerformanceConfiguration Performance { get; set; } = new();

    /// <summary>
    /// UI配置
    /// </summary>
    public UIConfiguration UI { get; set; } = new();

    /// <summary>
    /// 安全配置
    /// </summary>
    public SecurityConfiguration Security { get; set; } = new();

    /// <summary>
    /// 配置版本
    /// </summary>
    public string Version { get; set; } = "1.0";

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedAt { get; set; } = DateTime.Now;

    /// <summary>
    /// 最后修改时间
    /// </summary>
    public DateTime LastModified { get; set; } = DateTime.Now;

    /// <summary>
    /// 配置描述
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// 自定义设置
    /// </summary>
    public Dictionary<string, object> CustomSettings { get; set; } = new();
}

/// <summary>
/// 报告配置
/// </summary>
public class ReportConfiguration
{
    /// <summary>
    /// 输出目录
    /// </summary>
    public string OutputDirectory { get; set; } = "./Reports";

    /// <summary>
    /// 文件名模板
    /// </summary>
    public string FileNameTemplate { get; set; } = "{DriveLabel}_{DateTime:yyyy-MM-dd_HH-mm-ss}_Report";

    /// <summary>
    /// 是否包含系统信息
    /// </summary>
    public bool IncludeSystemInfo { get; set; } = true;

    /// <summary>
    /// 支持的格式
    /// </summary>
    public List<string> Formats { get; set; } = new() { "txt", "csv", "json" };

    /// <summary>
    /// 自动打开报告
    /// </summary>
    public bool AutoOpenReport { get; set; } = true;

    /// <summary>
    /// 报告模板路径
    /// </summary>
    public string? TemplatePath { get; set; }
}

/// <summary>
/// 日志配置
/// </summary>
public class LoggingConfiguration
{
    /// <summary>
    /// 日志级别
    /// </summary>
    public string LogLevel { get; set; } = "Information";

    /// <summary>
    /// 日志目录
    /// </summary>
    public string LogDirectory { get; set; } = "./Logs";

    /// <summary>
    /// 是否启用控制台输出
    /// </summary>
    public bool EnableConsoleOutput { get; set; } = true;

    /// <summary>
    /// 是否启用文件输出
    /// </summary>
    public bool EnableFileOutput { get; set; } = true;

    /// <summary>
    /// 日志文件最大大小（MB）
    /// </summary>
    public int MaxFileSizeMB { get; set; } = 10;

    /// <summary>
    /// 保留的日志文件数量
    /// </summary>
    public int RetainedFileCount { get; set; } = 7;
}

/// <summary>
/// 性能配置
/// </summary>
public class PerformanceConfiguration
{
    /// <summary>
    /// 最大并发测试数
    /// </summary>
    public int MaxConcurrentTests { get; set; } = 1;

    /// <summary>
    /// 进程清理超时时间（秒）
    /// </summary>
    public int ProcessCleanupTimeout { get; set; } = 30;

    /// <summary>
    /// 内存阈值（百分比）
    /// </summary>
    public int MemoryThreshold { get; set; } = 80;

    /// <summary>
    /// 是否启用性能监控
    /// </summary>
    public bool EnablePerformanceMonitoring { get; set; } = true;

    /// <summary>
    /// 监控间隔（秒）
    /// </summary>
    public int MonitoringInterval { get; set; } = 5;
}

/// <summary>
/// UI配置
/// </summary>
public class UIConfiguration
{
    /// <summary>
    /// 主题
    /// </summary>
    public string Theme { get; set; } = "Light";

    /// <summary>
    /// 语言
    /// </summary>
    public string Language { get; set; } = "zh-CN";

    /// <summary>
    /// 窗口大小
    /// </summary>
    public WindowSize WindowSize { get; set; } = new();

    /// <summary>
    /// 是否自动保存窗口状态
    /// </summary>
    public bool AutoSaveWindowState { get; set; } = true;

    /// <summary>
    /// 刷新间隔（毫秒）
    /// </summary>
    public int RefreshInterval { get; set; } = 1000;
}

/// <summary>
/// 窗口大小配置
/// </summary>
public class WindowSize
{
    /// <summary>
    /// 宽度
    /// </summary>
    public int Width { get; set; } = 1200;

    /// <summary>
    /// 高度
    /// </summary>
    public int Height { get; set; } = 800;

    /// <summary>
    /// 是否最大化
    /// </summary>
    public bool IsMaximized { get; set; } = false;
}

/// <summary>
/// 安全配置
/// </summary>
public class SecurityConfiguration
{
    /// <summary>
    /// 允许的可执行文件目录
    /// </summary>
    public List<string> AllowedExecutableDirectories { get; set; } = new();

    /// <summary>
    /// 是否验证数字签名
    /// </summary>
    public bool VerifyDigitalSignature { get; set; } = false;

    /// <summary>
    /// 是否限制驱动器访问
    /// </summary>
    public bool RestrictDriveAccess { get; set; } = true;

    /// <summary>
    /// 允许的驱动器类型
    /// </summary>
    public List<string> AllowedDriveTypes { get; set; } = new() { "Removable" };
}

/// <summary>
/// 配置历史记录
/// </summary>
public class ConfigurationHistory
{
    /// <summary>
    /// 版本号
    /// </summary>
    public string Version { get; set; } = string.Empty;

    /// <summary>
    /// 修改时间
    /// </summary>
    public DateTime ModifiedAt { get; set; }

    /// <summary>
    /// 修改描述
    /// </summary>
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// 配置文件路径
    /// </summary>
    public string FilePath { get; set; } = string.Empty;

    /// <summary>
    /// 文件大小
    /// </summary>
    public long FileSize { get; set; }
}

/// <summary>
/// 配置变化事件参数
/// </summary>
public class ConfigurationChangedEventArgs : EventArgs
{
    /// <summary>
    /// 变化类型
    /// </summary>
    public ConfigurationChangeType ChangeType { get; set; }

    /// <summary>
    /// 配置键
    /// </summary>
    public string ConfigKey { get; set; } = string.Empty;

    /// <summary>
    /// 旧值
    /// </summary>
    public object? OldValue { get; set; }

    /// <summary>
    /// 新值
    /// </summary>
    public object? NewValue { get; set; }

    /// <summary>
    /// 时间戳
    /// </summary>
    public DateTime Timestamp { get; set; } = DateTime.Now;
}

/// <summary>
/// 配置变化类型
/// </summary>
public enum ConfigurationChangeType
{
    /// <summary>
    /// 添加
    /// </summary>
    Added,

    /// <summary>
    /// 修改
    /// </summary>
    Modified,

    /// <summary>
    /// 删除
    /// </summary>
    Removed,

    /// <summary>
    /// 重置
    /// </summary>
    Reset
}
