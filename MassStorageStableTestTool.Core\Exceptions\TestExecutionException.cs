using MassStorageStableTestTool.Core.Enums;

namespace MassStorageStableTestTool.Core.Exceptions;

/// <summary>
/// 测试执行异常
/// </summary>
public class TestExecutionException : Exception
{
    /// <summary>
    /// 工具名称
    /// </summary>
    public string ToolName { get; }

    /// <summary>
    /// 测试状态
    /// </summary>
    public TestStatus Status { get; }

    /// <summary>
    /// 错误代码
    /// </summary>
    public int? ErrorCode { get; }

    public TestExecutionException(string toolName, string message) 
        : base($"测试工具 '{toolName}' 执行失败: {message}")
    {
        ToolName = toolName;
        Status = TestStatus.Failed;
    }

    public TestExecutionException(string toolName, string message, TestStatus status) 
        : base($"测试工具 '{toolName}' 执行失败: {message}")
    {
        ToolName = toolName;
        Status = status;
    }

    public TestExecutionException(string toolName, string message, int errorCode) 
        : base($"测试工具 '{toolName}' 执行失败: {message} (错误代码: {errorCode})")
    {
        ToolName = toolName;
        Status = TestStatus.Failed;
        ErrorCode = errorCode;
    }

    public TestExecutionException(string toolName, string message, Exception innerException) 
        : base($"测试工具 '{toolName}' 执行失败: {message}", innerException)
    {
        ToolName = toolName;
        Status = TestStatus.Failed;
    }

    public TestExecutionException(string toolName, string message, TestStatus status, Exception innerException) 
        : base($"测试工具 '{toolName}' 执行失败: {message}", innerException)
    {
        ToolName = toolName;
        Status = status;
    }
}
