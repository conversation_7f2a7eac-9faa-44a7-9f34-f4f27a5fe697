{"format": 1, "restore": {"C:\\Code\\MassStorageStableTestTool\\MassStorageStableTestTool.Automation\\MassStorageStableTestTool.Automation.csproj": {}}, "projects": {"C:\\Code\\MassStorageStableTestTool\\MassStorageStableTestTool.Automation\\MassStorageStableTestTool.Automation.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Code\\MassStorageStableTestTool\\MassStorageStableTestTool.Automation\\MassStorageStableTestTool.Automation.csproj", "projectName": "MassStorageStableTestTool.Automation", "projectPath": "C:\\Code\\MassStorageStableTestTool\\MassStorageStableTestTool.Automation\\MassStorageStableTestTool.Automation.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Code\\MassStorageStableTestTool\\MassStorageStableTestTool.Automation\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net8.0-windows"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "projectReferences": {"C:\\Code\\MassStorageStableTestTool\\MassStorageStableTestTool.Core\\MassStorageStableTestTool.Core.csproj": {"projectPath": "C:\\Code\\MassStorageStableTestTool\\MassStorageStableTestTool.Core\\MassStorageStableTestTool.Core.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Ref", "version": "[8.0.18, 8.0.18]"}, {"name": "Microsoft.NETCore.App.Ref", "version": "[8.0.18, 8.0.18]"}, {"name": "Microsoft.WindowsDesktop.App.Ref", "version": "[8.0.18, 8.0.18]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.302/PortableRuntimeIdentifierGraph.json"}}}, "C:\\Code\\MassStorageStableTestTool\\MassStorageStableTestTool.Core\\MassStorageStableTestTool.Core.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Code\\MassStorageStableTestTool\\MassStorageStableTestTool.Core\\MassStorageStableTestTool.Core.csproj", "projectName": "MassStorageStableTestTool.Core", "projectPath": "C:\\Code\\MassStorageStableTestTool\\MassStorageStableTestTool.Core\\MassStorageStableTestTool.Core.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Code\\MassStorageStableTestTool\\MassStorageStableTestTool.Core\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net8.0-windows"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.DependencyInjection.Abstractions": {"target": "Package", "version": "[8.0.1, )"}, "Microsoft.Extensions.Logging.Abstractions": {"target": "Package", "version": "[8.0.1, )"}, "System.Diagnostics.PerformanceCounter": {"target": "Package", "version": "[8.0.0, )"}, "System.Management": {"target": "Package", "version": "[8.0.0, )"}, "System.Text.Json": {"target": "Package", "version": "[8.0.5, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Ref", "version": "[8.0.18, 8.0.18]"}, {"name": "Microsoft.NETCore.App.Ref", "version": "[8.0.18, 8.0.18]"}, {"name": "Microsoft.WindowsDesktop.App.Ref", "version": "[8.0.18, 8.0.18]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.302/PortableRuntimeIdentifierGraph.json"}}}}}