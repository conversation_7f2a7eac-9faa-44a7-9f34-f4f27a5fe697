﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net8.0-windows</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <UseWPF>true</UseWPF>
    <ApplicationIcon>favicon.ico</ApplicationIcon>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Logging" Version="8.0.0" />
    <PackageReference Include="NLog.Extensions.Logging" Version="5.3.5" />
    <PackageReference Include="NLog" Version="5.2.5" />
    <PackageReference Include="CommunityToolkit.Mvvm" Version="8.2.2" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\MassStorageStableTestTool.Core\MassStorageStableTestTool.Core.csproj" />
    <ProjectReference Include="..\MassStorageStableTestTool.Automation\MassStorageStableTestTool.Automation.csproj" />
    <ProjectReference Include="..\MassStorageStableTestTool.Reports\MassStorageStableTestTool.Reports.csproj" />
  </ItemGroup>

</Project>
