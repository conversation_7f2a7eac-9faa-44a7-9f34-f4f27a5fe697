namespace MassStorageStableTestTool.Core.Exceptions;

/// <summary>
/// 测试超时异常
/// </summary>
public class TestTimeoutException : Exception
{
    /// <summary>
    /// 工具名称
    /// </summary>
    public string ToolName { get; }

    /// <summary>
    /// 超时时间
    /// </summary>
    public TimeSpan Timeout { get; }

    /// <summary>
    /// 操作类型
    /// </summary>
    public string? OperationType { get; }

    public TestTimeoutException(string toolName, TimeSpan timeout) 
        : base($"测试工具 '{toolName}' 在 {timeout.TotalSeconds} 秒后超时")
    {
        ToolName = toolName;
        Timeout = timeout;
    }

    public TestTimeoutException(string toolName, TimeSpan timeout, string operationType) 
        : base($"测试工具 '{toolName}' 的 '{operationType}' 操作在 {timeout.TotalSeconds} 秒后超时")
    {
        ToolName = toolName;
        Timeout = timeout;
        OperationType = operationType;
    }

    public TestTimeoutException(string toolName, TimeSpan timeout, Exception innerException) 
        : base($"测试工具 '{toolName}' 在 {timeout.TotalSeconds} 秒后超时", innerException)
    {
        ToolName = toolName;
        Timeout = timeout;
    }

    public TestTimeoutException(string toolName, TimeSpan timeout, string operationType, Exception innerException) 
        : base($"测试工具 '{toolName}' 的 '{operationType}' 操作在 {timeout.TotalSeconds} 秒后超时", innerException)
    {
        ToolName = toolName;
        Timeout = timeout;
        OperationType = operationType;
    }
}
