namespace MassStorageStableTestTool.Core.Exceptions;

/// <summary>
/// 配置异常
/// </summary>
public class ConfigurationException : Exception
{
    /// <summary>
    /// 配置键名
    /// </summary>
    public string? ConfigKey { get; }

    /// <summary>
    /// 配置文件路径
    /// </summary>
    public string? ConfigFilePath { get; }

    public ConfigurationException(string message) 
        : base($"配置错误: {message}")
    {
    }

    public ConfigurationException(string configKey, string message) 
        : base($"配置项 '{configKey}' 错误: {message}")
    {
        ConfigKey = configKey;
    }

    public ConfigurationException(string configKey, string configFilePath, string message) 
        : base($"配置文件 '{configFilePath}' 中的配置项 '{configKey}' 错误: {message}")
    {
        ConfigKey = configKey;
        ConfigFilePath = configFilePath;
    }

    public ConfigurationException(string message, Exception innerException) 
        : base($"配置错误: {message}", innerException)
    {
    }

    public ConfigurationException(string configKey, string message, Exception innerException) 
        : base($"配置项 '{configKey}' 错误: {message}", innerException)
    {
        ConfigKey = configKey;
    }
}
