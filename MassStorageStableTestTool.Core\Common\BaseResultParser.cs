using MassStorageStableTestTool.Core.Interfaces;
using MassStorageStableTestTool.Core.Models;
using System.Text.RegularExpressions;

namespace MassStorageStableTestTool.Core.Common;

/// <summary>
/// 结果解析器基类
/// </summary>
/// <typeparam name="T">测试结果类型</typeparam>
public abstract class BaseResultParser<T> : IResultParser<T> where T : TestResult, new()
{
    /// <summary>
    /// 支持的工具名称
    /// </summary>
    public abstract string SupportedToolName { get; }

    /// <summary>
    /// 支持的输出格式
    /// </summary>
    public virtual List<string> SupportedFormats => new() { "text", "json", "xml", "csv" };

    /// <summary>
    /// 从文本输出解析结果
    /// </summary>
    /// <param name="output">输出文本</param>
    /// <param name="format">输出格式</param>
    /// <returns>解析后的测试结果</returns>
    public async Task<T> ParseFromTextAsync(string output, string format = "text")
    {
        if (string.IsNullOrWhiteSpace(output))
        {
            throw new ArgumentException("输出内容不能为空", nameof(output));
        }

        if (!IsFormatSupported(format))
        {
            throw new ArgumentException($"不支持的格式: {format}", nameof(format));
        }

        var result = new T
        {
            ToolName = SupportedToolName,
            StartTime = DateTime.Now,
            EndTime = DateTime.Now
        };

        try
        {
            // 根据格式选择解析方法
            switch (format.ToLowerInvariant())
            {
                case "json":
                    result = await ParseJsonOutputAsync(output);
                    break;
                case "xml":
                    result = await ParseXmlOutputAsync(output);
                    break;
                case "csv":
                    result = await ParseCsvOutputAsync(output);
                    break;
                case "text":
                default:
                    result = await ParseTextOutputAsync(output);
                    break;
            }

            // 提取性能指标
            result.Performance = await ExtractPerformanceMetricsAsync(output);
            result.Success = true;
        }
        catch (Exception ex)
        {
            result.Success = false;
            result.ErrorMessage = $"解析输出时出现错误: {ex.Message}";
            result.Exception = ex;
        }

        return result;
    }

    /// <summary>
    /// 从文件解析结果
    /// </summary>
    /// <param name="filePath">文件路径</param>
    /// <param name="format">文件格式</param>
    /// <returns>解析后的测试结果</returns>
    public async Task<T> ParseFromFileAsync(string filePath, string? format = null)
    {
        if (!File.Exists(filePath))
        {
            throw new FileNotFoundException($"文件不存在: {filePath}");
        }

        // 如果未指定格式，从文件扩展名推断
        if (string.IsNullOrEmpty(format))
        {
            format = Path.GetExtension(filePath).TrimStart('.').ToLowerInvariant();
            if (string.IsNullOrEmpty(format))
            {
                format = "text";
            }
        }

        var content = await File.ReadAllTextAsync(filePath);
        var result = await ParseFromTextAsync(content, format);
        
        // 添加文件信息
        result.OutputFiles.Add(filePath);
        result.AddLog($"从文件解析结果: {filePath}");

        return result;
    }

    /// <summary>
    /// 从进程结果解析
    /// </summary>
    /// <param name="processResult">进程执行结果</param>
    /// <returns>解析后的测试结果</returns>
    public async Task<T> ParseFromProcessResultAsync(ProcessResult processResult)
    {
        if (processResult == null)
        {
            throw new ArgumentNullException(nameof(processResult));
        }

        var result = await ParseFromTextAsync(processResult.StandardOutput);
        
        // 添加进程信息
        result.StartTime = processResult.StartTime;
        result.EndTime = processResult.EndTime;
        result.AddLog($"进程退出码: {processResult.ExitCode}");
        
        if (processResult.HasError)
        {
            result.AddWarning($"进程错误输出: {processResult.StandardError}");
        }

        // 如果进程失败，标记结果为失败
        if (!processResult.IsSuccess)
        {
            result.Success = false;
            result.ErrorMessage = $"进程执行失败，退出码: {processResult.ExitCode}";
        }

        return result;
    }

    /// <summary>
    /// 验证输出格式是否受支持
    /// </summary>
    /// <param name="format">输出格式</param>
    /// <returns>是否支持</returns>
    public virtual bool IsFormatSupported(string format)
    {
        return SupportedFormats.Contains(format.ToLowerInvariant());
    }

    /// <summary>
    /// 检查是否可以解析指定工具的结果
    /// </summary>
    /// <param name="toolName">工具名称</param>
    /// <returns>是否可以解析</returns>
    public virtual bool CanParseResult(string toolName)
    {
        return string.Equals(SupportedToolName, toolName, StringComparison.OrdinalIgnoreCase);
    }

    /// <summary>
    /// 获取解析器信息
    /// </summary>
    /// <returns>解析器信息</returns>
    public virtual ParserInfo GetParserInfo()
    {
        return new ParserInfo
        {
            Name = GetType().Name,
            Version = "1.0",
            SupportedTools = new List<string> { SupportedToolName },
            SupportedFormats = SupportedFormats,
            Description = $"{SupportedToolName} 结果解析器",
            Author = "MassStorageStableTestTool",
            CreatedAt = DateTime.Now
        };
    }

    /// <summary>
    /// 验证输出内容
    /// </summary>
    /// <param name="output">输出内容</param>
    /// <returns>验证结果</returns>
    public virtual (bool IsValid, List<string> Errors) ValidateOutput(string output)
    {
        var errors = new List<string>();

        if (string.IsNullOrWhiteSpace(output))
        {
            errors.Add("输出内容为空");
        }

        // 子类可以重写此方法添加特定的验证逻辑

        return (errors.Count == 0, errors);
    }

    /// <summary>
    /// 提取关键性能指标
    /// </summary>
    /// <param name="output">输出内容</param>
    /// <returns>性能指标</returns>
    public virtual async Task<PerformanceMetrics?> ExtractPerformanceMetricsAsync(string output)
    {
        // 子类应该重写此方法实现具体的性能指标提取逻辑
        return null;
    }

    /// <summary>
    /// 解析文本格式输出（由子类实现）
    /// </summary>
    /// <param name="output">文本输出</param>
    /// <returns>解析结果</returns>
    protected abstract Task<T> ParseTextOutputAsync(string output);

    /// <summary>
    /// 解析JSON格式输出
    /// </summary>
    /// <param name="output">JSON输出</param>
    /// <returns>解析结果</returns>
    protected virtual async Task<T> ParseJsonOutputAsync(string output)
    {
        // 默认实现，子类可以重写
        return await ParseTextOutputAsync(output);
    }

    /// <summary>
    /// 解析XML格式输出
    /// </summary>
    /// <param name="output">XML输出</param>
    /// <returns>解析结果</returns>
    protected virtual async Task<T> ParseXmlOutputAsync(string output)
    {
        // 默认实现，子类可以重写
        return await ParseTextOutputAsync(output);
    }

    /// <summary>
    /// 解析CSV格式输出
    /// </summary>
    /// <param name="output">CSV输出</param>
    /// <returns>解析结果</returns>
    protected virtual async Task<T> ParseCsvOutputAsync(string output)
    {
        // 默认实现，子类可以重写
        return await ParseTextOutputAsync(output);
    }

    /// <summary>
    /// 使用正则表达式提取数值
    /// </summary>
    /// <param name="input">输入文本</param>
    /// <param name="pattern">正则表达式模式</param>
    /// <param name="groupName">组名</param>
    /// <returns>提取的数值</returns>
    protected virtual double? ExtractNumericValue(string input, string pattern, string groupName = "value")
    {
        try
        {
            var match = Regex.Match(input, pattern, RegexOptions.IgnoreCase | RegexOptions.Multiline);
            if (match.Success && match.Groups[groupName].Success)
            {
                var valueText = match.Groups[groupName].Value;
                if (double.TryParse(valueText, out var value))
                {
                    return value;
                }
            }
        }
        catch
        {
            // 忽略解析错误
        }

        return null;
    }

    /// <summary>
    /// 使用正则表达式提取文本值
    /// </summary>
    /// <param name="input">输入文本</param>
    /// <param name="pattern">正则表达式模式</param>
    /// <param name="groupName">组名</param>
    /// <returns>提取的文本</returns>
    protected virtual string? ExtractTextValue(string input, string pattern, string groupName = "value")
    {
        try
        {
            var match = Regex.Match(input, pattern, RegexOptions.IgnoreCase | RegexOptions.Multiline);
            if (match.Success && match.Groups[groupName].Success)
            {
                return match.Groups[groupName].Value.Trim();
            }
        }
        catch
        {
            // 忽略解析错误
        }

        return null;
    }

    /// <summary>
    /// 分割输出为行
    /// </summary>
    /// <param name="output">输出内容</param>
    /// <param name="removeEmpty">是否移除空行</param>
    /// <returns>行列表</returns>
    protected virtual List<string> SplitIntoLines(string output, bool removeEmpty = true)
    {
        var lines = output.Split(new[] { '\r', '\n' }, StringSplitOptions.None);
        
        if (removeEmpty)
        {
            lines = lines.Where(line => !string.IsNullOrWhiteSpace(line)).ToArray();
        }
        
        return lines.ToList();
    }

    /// <summary>
    /// 查找包含指定文本的行
    /// </summary>
    /// <param name="lines">行列表</param>
    /// <param name="searchText">搜索文本</param>
    /// <param name="ignoreCase">是否忽略大小写</param>
    /// <returns>匹配的行列表</returns>
    protected virtual List<string> FindLinesContaining(List<string> lines, string searchText, bool ignoreCase = true)
    {
        var comparison = ignoreCase ? StringComparison.OrdinalIgnoreCase : StringComparison.Ordinal;
        return lines.Where(line => line.Contains(searchText, comparison)).ToList();
    }
}
