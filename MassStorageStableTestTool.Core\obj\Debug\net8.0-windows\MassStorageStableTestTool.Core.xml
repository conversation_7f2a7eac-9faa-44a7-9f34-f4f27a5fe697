<?xml version="1.0"?>
<doc>
    <assembly>
        <name>MassStorageStableTestTool.Core</name>
    </assembly>
    <members>
        <member name="T:MassStorageStableTestTool.Core.Common.BaseResultParser`1">
            <summary>
            结果解析器基类
            </summary>
            <typeparam name="T">测试结果类型</typeparam>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Common.BaseResultParser`1.SupportedToolName">
            <summary>
            支持的工具名称
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Common.BaseResultParser`1.SupportedFormats">
            <summary>
            支持的输出格式
            </summary>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Common.BaseResultParser`1.ParseFromTextAsync(System.String,System.String)">
            <summary>
            从文本输出解析结果
            </summary>
            <param name="output">输出文本</param>
            <param name="format">输出格式</param>
            <returns>解析后的测试结果</returns>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Common.BaseResultParser`1.ParseFromFileAsync(System.String,System.String)">
            <summary>
            从文件解析结果
            </summary>
            <param name="filePath">文件路径</param>
            <param name="format">文件格式</param>
            <returns>解析后的测试结果</returns>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Common.BaseResultParser`1.ParseFromProcessResultAsync(MassStorageStableTestTool.Core.Models.ProcessResult)">
            <summary>
            从进程结果解析
            </summary>
            <param name="processResult">进程执行结果</param>
            <returns>解析后的测试结果</returns>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Common.BaseResultParser`1.IsFormatSupported(System.String)">
            <summary>
            验证输出格式是否受支持
            </summary>
            <param name="format">输出格式</param>
            <returns>是否支持</returns>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Common.BaseResultParser`1.CanParseResult(System.String)">
            <summary>
            检查是否可以解析指定工具的结果
            </summary>
            <param name="toolName">工具名称</param>
            <returns>是否可以解析</returns>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Common.BaseResultParser`1.GetParserInfo">
            <summary>
            获取解析器信息
            </summary>
            <returns>解析器信息</returns>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Common.BaseResultParser`1.ValidateOutput(System.String)">
            <summary>
            验证输出内容
            </summary>
            <param name="output">输出内容</param>
            <returns>验证结果</returns>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Common.BaseResultParser`1.ExtractPerformanceMetricsAsync(System.String)">
            <summary>
            提取关键性能指标
            </summary>
            <param name="output">输出内容</param>
            <returns>性能指标</returns>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Common.BaseResultParser`1.ParseTextOutputAsync(System.String)">
            <summary>
            解析文本格式输出（由子类实现）
            </summary>
            <param name="output">文本输出</param>
            <returns>解析结果</returns>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Common.BaseResultParser`1.ParseJsonOutputAsync(System.String)">
            <summary>
            解析JSON格式输出
            </summary>
            <param name="output">JSON输出</param>
            <returns>解析结果</returns>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Common.BaseResultParser`1.ParseXmlOutputAsync(System.String)">
            <summary>
            解析XML格式输出
            </summary>
            <param name="output">XML输出</param>
            <returns>解析结果</returns>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Common.BaseResultParser`1.ParseCsvOutputAsync(System.String)">
            <summary>
            解析CSV格式输出
            </summary>
            <param name="output">CSV输出</param>
            <returns>解析结果</returns>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Common.BaseResultParser`1.ExtractNumericValue(System.String,System.String,System.String)">
            <summary>
            使用正则表达式提取数值
            </summary>
            <param name="input">输入文本</param>
            <param name="pattern">正则表达式模式</param>
            <param name="groupName">组名</param>
            <returns>提取的数值</returns>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Common.BaseResultParser`1.ExtractTextValue(System.String,System.String,System.String)">
            <summary>
            使用正则表达式提取文本值
            </summary>
            <param name="input">输入文本</param>
            <param name="pattern">正则表达式模式</param>
            <param name="groupName">组名</param>
            <returns>提取的文本</returns>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Common.BaseResultParser`1.SplitIntoLines(System.String,System.Boolean)">
            <summary>
            分割输出为行
            </summary>
            <param name="output">输出内容</param>
            <param name="removeEmpty">是否移除空行</param>
            <returns>行列表</returns>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Common.BaseResultParser`1.FindLinesContaining(System.Collections.Generic.List{System.String},System.String,System.Boolean)">
            <summary>
            查找包含指定文本的行
            </summary>
            <param name="lines">行列表</param>
            <param name="searchText">搜索文本</param>
            <param name="ignoreCase">是否忽略大小写</param>
            <returns>匹配的行列表</returns>
        </member>
        <member name="T:MassStorageStableTestTool.Core.Common.BaseTestToolController">
            <summary>
            测试工具控制器基类
            </summary>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Common.BaseTestToolController.#ctor(MassStorageStableTestTool.Core.Models.TestToolConfig)">
            <summary>
            构造函数
            </summary>
            <param name="configuration">工具配置</param>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Common.BaseTestToolController.ToolName">
            <summary>
            工具名称
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Common.BaseTestToolController.ToolType">
            <summary>
            工具类型
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Common.BaseTestToolController.Configuration">
            <summary>
            工具配置
            </summary>
        </member>
        <member name="E:MassStorageStableTestTool.Core.Common.BaseTestToolController.StatusChanged">
            <summary>
            状态变化事件
            </summary>
        </member>
        <member name="E:MassStorageStableTestTool.Core.Common.BaseTestToolController.LogReceived">
            <summary>
            日志事件
            </summary>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Common.BaseTestToolController.ExecuteTestAsync(MassStorageStableTestTool.Core.Models.TestConfiguration,System.Threading.CancellationToken,System.IProgress{MassStorageStableTestTool.Core.Interfaces.ProgressEventArgs})">
            <summary>
            执行测试
            </summary>
            <param name="config">测试配置</param>
            <param name="cancellationToken">取消令牌</param>
            <param name="progress">进度报告器</param>
            <returns>测试结果</returns>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Common.BaseTestToolController.ExecuteTestInternalAsync(MassStorageStableTestTool.Core.Models.TestConfiguration,System.Threading.CancellationToken,System.IProgress{MassStorageStableTestTool.Core.Interfaces.ProgressEventArgs})">
            <summary>
            执行具体的测试逻辑（由子类实现）
            </summary>
            <param name="config">测试配置</param>
            <param name="cancellationToken">取消令牌</param>
            <param name="progress">进度报告器</param>
            <returns>测试结果</returns>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Common.BaseTestToolController.IsToolAvailable">
            <summary>
            检查工具是否可用
            </summary>
            <returns>是否可用</returns>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Common.BaseTestToolController.GetToolVersionAsync">
            <summary>
            获取工具版本
            </summary>
            <returns>工具版本</returns>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Common.BaseTestToolController.ValidateConfiguration(MassStorageStableTestTool.Core.Models.TestConfiguration)">
            <summary>
            验证测试配置
            </summary>
            <param name="config">测试配置</param>
            <returns>验证结果</returns>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Common.BaseTestToolController.ValidateToolSpecificConfiguration(MassStorageStableTestTool.Core.Models.TestConfiguration)">
            <summary>
            验证工具特定的配置（由子类重写）
            </summary>
            <param name="config">测试配置</param>
            <returns>错误列表</returns>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Common.BaseTestToolController.PrepareTestEnvironmentAsync(MassStorageStableTestTool.Core.Models.TestConfiguration,System.Threading.CancellationToken)">
            <summary>
            准备测试环境
            </summary>
            <param name="config">测试配置</param>
            <param name="cancellationToken">取消令牌</param>
            <returns>准备结果</returns>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Common.BaseTestToolController.CleanupTestEnvironmentAsync(MassStorageStableTestTool.Core.Models.TestConfiguration,System.Threading.CancellationToken)">
            <summary>
            清理测试环境
            </summary>
            <param name="config">测试配置</param>
            <param name="cancellationToken">取消令牌</param>
            <returns>清理结果</returns>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Common.BaseTestToolController.StopTestAsync">
            <summary>
            停止正在运行的测试
            </summary>
            <returns>停止结果</returns>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Common.BaseTestToolController.GetCurrentStatus">
            <summary>
            获取当前测试状态
            </summary>
            <returns>测试状态</returns>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Common.BaseTestToolController.GetSupportedParameters">
            <summary>
            获取支持的参数列表
            </summary>
            <returns>支持的参数</returns>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Common.BaseTestToolController.UpdateStatus(MassStorageStableTestTool.Core.Enums.TestStatus)">
            <summary>
            更新状态
            </summary>
            <param name="newStatus">新状态</param>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Common.BaseTestToolController.LogInfo(System.String)">
            <summary>
            记录信息日志
            </summary>
            <param name="message">日志消息</param>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Common.BaseTestToolController.LogWarning(System.String)">
            <summary>
            记录警告日志
            </summary>
            <param name="message">日志消息</param>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Common.BaseTestToolController.LogError(System.String,System.Exception)">
            <summary>
            记录错误日志
            </summary>
            <param name="message">日志消息</param>
            <param name="exception">异常信息</param>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Common.BaseTestToolController.Dispose">
            <summary>
            释放资源
            </summary>
        </member>
        <member name="T:MassStorageStableTestTool.Core.Enums.ReportFormat">
            <summary>
            报告格式枚举
            </summary>
        </member>
        <member name="F:MassStorageStableTestTool.Core.Enums.ReportFormat.Text">
            <summary>
            纯文本格式
            </summary>
        </member>
        <member name="F:MassStorageStableTestTool.Core.Enums.ReportFormat.CSV">
            <summary>
            CSV格式
            </summary>
        </member>
        <member name="F:MassStorageStableTestTool.Core.Enums.ReportFormat.JSON">
            <summary>
            JSON格式
            </summary>
        </member>
        <member name="F:MassStorageStableTestTool.Core.Enums.ReportFormat.HTML">
            <summary>
            HTML格式
            </summary>
        </member>
        <member name="F:MassStorageStableTestTool.Core.Enums.ReportFormat.XML">
            <summary>
            XML格式
            </summary>
        </member>
        <member name="T:MassStorageStableTestTool.Core.Enums.TestStatus">
            <summary>
            测试状态枚举
            </summary>
        </member>
        <member name="F:MassStorageStableTestTool.Core.Enums.TestStatus.NotStarted">
            <summary>
            未开始
            </summary>
        </member>
        <member name="F:MassStorageStableTestTool.Core.Enums.TestStatus.Preparing">
            <summary>
            准备中
            </summary>
        </member>
        <member name="F:MassStorageStableTestTool.Core.Enums.TestStatus.Running">
            <summary>
            运行中
            </summary>
        </member>
        <member name="F:MassStorageStableTestTool.Core.Enums.TestStatus.Completed">
            <summary>
            已完成
            </summary>
        </member>
        <member name="F:MassStorageStableTestTool.Core.Enums.TestStatus.Failed">
            <summary>
            失败
            </summary>
        </member>
        <member name="F:MassStorageStableTestTool.Core.Enums.TestStatus.Cancelled">
            <summary>
            已取消
            </summary>
        </member>
        <member name="F:MassStorageStableTestTool.Core.Enums.TestStatus.Timeout">
            <summary>
            超时
            </summary>
        </member>
        <member name="T:MassStorageStableTestTool.Core.Enums.TestToolType">
            <summary>
            测试工具类型枚举
            </summary>
        </member>
        <member name="F:MassStorageStableTestTool.Core.Enums.TestToolType.GUI">
            <summary>
            GUI工具 - 需要GUI自动化技术控制
            </summary>
        </member>
        <member name="F:MassStorageStableTestTool.Core.Enums.TestToolType.CLI">
            <summary>
            CLI工具 - 支持命令行调用
            </summary>
        </member>
        <member name="F:MassStorageStableTestTool.Core.Enums.TestToolType.Hybrid">
            <summary>
            混合模式工具 - 既支持GUI也支持CLI
            </summary>
        </member>
        <member name="T:MassStorageStableTestTool.Core.Exceptions.ConfigurationException">
            <summary>
            配置异常
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Exceptions.ConfigurationException.ConfigKey">
            <summary>
            配置键名
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Exceptions.ConfigurationException.ConfigFilePath">
            <summary>
            配置文件路径
            </summary>
        </member>
        <member name="T:MassStorageStableTestTool.Core.Exceptions.ElementNotFoundException">
            <summary>
            UI元素未找到异常
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Exceptions.ElementNotFoundException.ElementIdentifier">
            <summary>
            元素标识符
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Exceptions.ElementNotFoundException.WindowTitle">
            <summary>
            窗口标题
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Exceptions.ElementNotFoundException.Timeout">
            <summary>
            超时时间
            </summary>
        </member>
        <member name="T:MassStorageStableTestTool.Core.Exceptions.TestExecutionException">
            <summary>
            测试执行异常
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Exceptions.TestExecutionException.ToolName">
            <summary>
            工具名称
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Exceptions.TestExecutionException.Status">
            <summary>
            测试状态
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Exceptions.TestExecutionException.ErrorCode">
            <summary>
            错误代码
            </summary>
        </member>
        <member name="T:MassStorageStableTestTool.Core.Exceptions.TestTimeoutException">
            <summary>
            测试超时异常
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Exceptions.TestTimeoutException.ToolName">
            <summary>
            工具名称
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Exceptions.TestTimeoutException.Timeout">
            <summary>
            超时时间
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Exceptions.TestTimeoutException.OperationType">
            <summary>
            操作类型
            </summary>
        </member>
        <member name="T:MassStorageStableTestTool.Core.Exceptions.TestToolNotFoundException">
            <summary>
            测试工具未找到异常
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Exceptions.TestToolNotFoundException.ToolName">
            <summary>
            工具名称
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Exceptions.TestToolNotFoundException.ToolPath">
            <summary>
            工具路径
            </summary>
        </member>
        <member name="T:MassStorageStableTestTool.Core.Interfaces.IConfigurationService">
            <summary>
            配置服务接口
            </summary>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Interfaces.IConfigurationService.LoadConfigurationAsync(System.String)">
            <summary>
            加载配置
            </summary>
            <param name="configPath">配置文件路径</param>
            <returns>配置对象</returns>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Interfaces.IConfigurationService.SaveConfigurationAsync(MassStorageStableTestTool.Core.Models.ApplicationConfiguration,System.String)">
            <summary>
            保存配置
            </summary>
            <param name="configuration">配置对象</param>
            <param name="configPath">配置文件路径</param>
            <returns>保存结果</returns>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Interfaces.IConfigurationService.CreateDefaultConfiguration">
            <summary>
            创建默认配置
            </summary>
            <returns>默认配置</returns>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Interfaces.IConfigurationService.ValidateConfiguration(MassStorageStableTestTool.Core.Models.ApplicationConfiguration)">
            <summary>
            验证配置
            </summary>
            <param name="configuration">配置对象</param>
            <returns>验证结果</returns>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Interfaces.IConfigurationService.GetToolConfigurationAsync(System.String)">
            <summary>
            获取测试工具配置
            </summary>
            <param name="toolName">工具名称</param>
            <returns>工具配置</returns>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Interfaces.IConfigurationService.UpdateToolConfigurationAsync(System.String,MassStorageStableTestTool.Core.Models.TestToolConfig)">
            <summary>
            更新测试工具配置
            </summary>
            <param name="toolName">工具名称</param>
            <param name="config">工具配置</param>
            <returns>更新结果</returns>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Interfaces.IConfigurationService.GetAllToolConfigurationsAsync">
            <summary>
            获取所有测试工具配置
            </summary>
            <returns>工具配置字典</returns>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Interfaces.IConfigurationService.ImportConfigurationAsync(System.String,System.String)">
            <summary>
            导入配置
            </summary>
            <param name="filePath">配置文件路径</param>
            <param name="format">配置格式</param>
            <returns>导入的配置</returns>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Interfaces.IConfigurationService.ExportConfigurationAsync(MassStorageStableTestTool.Core.Models.ApplicationConfiguration,System.String,System.String)">
            <summary>
            导出配置
            </summary>
            <param name="configuration">配置对象</param>
            <param name="filePath">导出文件路径</param>
            <param name="format">导出格式</param>
            <returns>导出结果</returns>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Interfaces.IConfigurationService.ResetToDefaultAsync">
            <summary>
            重置配置为默认值
            </summary>
            <returns>重置结果</returns>
        </member>
        <member name="E:MassStorageStableTestTool.Core.Interfaces.IConfigurationService.ConfigurationChanged">
            <summary>
            配置变化事件
            </summary>
        </member>
        <member name="T:MassStorageStableTestTool.Core.Interfaces.ConfigurationChangedEventArgs">
            <summary>
            配置变化事件参数
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Interfaces.ConfigurationChangedEventArgs.ChangeType">
            <summary>
            变化类型
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Interfaces.ConfigurationChangedEventArgs.ConfigKey">
            <summary>
            配置键
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Interfaces.ConfigurationChangedEventArgs.OldValue">
            <summary>
            旧值
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Interfaces.ConfigurationChangedEventArgs.NewValue">
            <summary>
            新值
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Interfaces.ConfigurationChangedEventArgs.Timestamp">
            <summary>
            时间戳
            </summary>
        </member>
        <member name="T:MassStorageStableTestTool.Core.Interfaces.ConfigurationChangeType">
            <summary>
            配置变化类型
            </summary>
        </member>
        <member name="F:MassStorageStableTestTool.Core.Interfaces.ConfigurationChangeType.Added">
            <summary>
            添加
            </summary>
        </member>
        <member name="F:MassStorageStableTestTool.Core.Interfaces.ConfigurationChangeType.Modified">
            <summary>
            修改
            </summary>
        </member>
        <member name="F:MassStorageStableTestTool.Core.Interfaces.ConfigurationChangeType.Removed">
            <summary>
            删除
            </summary>
        </member>
        <member name="F:MassStorageStableTestTool.Core.Interfaces.ConfigurationChangeType.Reset">
            <summary>
            重置
            </summary>
        </member>
        <member name="T:MassStorageStableTestTool.Core.Interfaces.IReportService">
            <summary>
            报告服务接口
            </summary>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Interfaces.IReportService.GenerateReportAsync(MassStorageStableTestTool.Core.Models.TestSuiteResult,MassStorageStableTestTool.Core.Enums.ReportFormat)">
            <summary>
            生成测试报告
            </summary>
            <param name="testSuiteResult">测试套件结果</param>
            <param name="format">报告格式</param>
            <returns>报告内容</returns>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Interfaces.IReportService.GenerateAndSaveReportAsync(MassStorageStableTestTool.Core.Models.TestSuiteResult,System.String,MassStorageStableTestTool.Core.Enums.ReportFormat)">
            <summary>
            生成并保存报告
            </summary>
            <param name="testSuiteResult">测试套件结果</param>
            <param name="outputPath">输出路径</param>
            <param name="format">报告格式</param>
            <returns>保存的文件路径</returns>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Interfaces.IReportService.ExportReportAsync(System.String,System.String,MassStorageStableTestTool.Core.Enums.ReportFormat)">
            <summary>
            导出报告到文件
            </summary>
            <param name="reportContent">报告内容</param>
            <param name="filePath">文件路径</param>
            <param name="format">报告格式</param>
            <returns>导出结果</returns>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Interfaces.IReportService.GetReportTemplateAsync(System.String)">
            <summary>
            获取报告模板
            </summary>
            <param name="templateName">模板名称</param>
            <returns>模板内容</returns>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Interfaces.IReportService.SetReportTemplateAsync(System.String,System.String)">
            <summary>
            设置报告模板
            </summary>
            <param name="templateName">模板名称</param>
            <param name="templateContent">模板内容</param>
            <returns>设置结果</returns>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Interfaces.IReportService.GetAvailableTemplatesAsync">
            <summary>
            获取可用的报告模板列表
            </summary>
            <returns>模板名称列表</returns>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Interfaces.IReportService.GenerateSummaryReportAsync(MassStorageStableTestTool.Core.Models.TestSuiteResult)">
            <summary>
            生成摘要报告
            </summary>
            <param name="testSuiteResult">测试套件结果</param>
            <returns>摘要报告内容</returns>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Interfaces.IReportService.GenerateDetailedReportAsync(MassStorageStableTestTool.Core.Models.TestSuiteResult)">
            <summary>
            生成详细报告
            </summary>
            <param name="testSuiteResult">测试套件结果</param>
            <returns>详细报告内容</returns>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Interfaces.IReportService.GeneratePerformanceReportAsync(MassStorageStableTestTool.Core.Models.TestSuiteResult)">
            <summary>
            生成性能报告
            </summary>
            <param name="testSuiteResult">测试套件结果</param>
            <returns>性能报告内容</returns>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Interfaces.IReportService.GenerateComparisonReportAsync(System.Collections.Generic.List{MassStorageStableTestTool.Core.Models.TestSuiteResult})">
            <summary>
            生成比较报告
            </summary>
            <param name="testResults">测试结果列表</param>
            <returns>比较报告内容</returns>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Interfaces.IReportService.SendReportEmailAsync(System.String,System.Collections.Generic.List{System.String},System.String)">
            <summary>
            发送报告邮件
            </summary>
            <param name="reportContent">报告内容</param>
            <param name="recipients">收件人列表</param>
            <param name="subject">邮件主题</param>
            <returns>发送结果</returns>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Interfaces.IReportService.GetReportHistoryAsync(System.Int32)">
            <summary>
            获取报告历史
            </summary>
            <param name="count">历史记录数量</param>
            <returns>报告历史列表</returns>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Interfaces.IReportService.DeleteReportAsync(System.String)">
            <summary>
            删除报告文件
            </summary>
            <param name="filePath">文件路径</param>
            <returns>删除结果</returns>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Interfaces.IReportService.CleanupExpiredReportsAsync(System.Int32)">
            <summary>
            清理过期报告
            </summary>
            <param name="retentionDays">保留天数</param>
            <returns>清理的文件数量</returns>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Interfaces.IReportService.ValidateReport(System.String,MassStorageStableTestTool.Core.Enums.ReportFormat)">
            <summary>
            验证报告内容
            </summary>
            <param name="reportContent">报告内容</param>
            <param name="format">报告格式</param>
            <returns>验证结果</returns>
        </member>
        <member name="E:MassStorageStableTestTool.Core.Interfaces.IReportService.ReportGenerated">
            <summary>
            报告生成完成事件
            </summary>
        </member>
        <member name="T:MassStorageStableTestTool.Core.Interfaces.ReportHistory">
            <summary>
            报告历史记录
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Interfaces.ReportHistory.Id">
            <summary>
            报告ID
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Interfaces.ReportHistory.FilePath">
            <summary>
            报告文件路径
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Interfaces.ReportHistory.Format">
            <summary>
            报告格式
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Interfaces.ReportHistory.GeneratedAt">
            <summary>
            生成时间
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Interfaces.ReportHistory.ConfigurationName">
            <summary>
            测试配置名称
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Interfaces.ReportHistory.TargetDrive">
            <summary>
            目标驱动器
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Interfaces.ReportHistory.TestTools">
            <summary>
            测试工具列表
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Interfaces.ReportHistory.Summary">
            <summary>
            测试结果摘要
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Interfaces.ReportHistory.FileSize">
            <summary>
            文件大小（字节）
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Interfaces.ReportHistory.IsSuccess">
            <summary>
            是否成功
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Interfaces.ReportHistory.Tags">
            <summary>
            标签
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Interfaces.ReportHistory.Notes">
            <summary>
            备注
            </summary>
        </member>
        <member name="T:MassStorageStableTestTool.Core.Interfaces.ReportGeneratedEventArgs">
            <summary>
            报告生成事件参数
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Interfaces.ReportGeneratedEventArgs.FilePath">
            <summary>
            报告文件路径
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Interfaces.ReportGeneratedEventArgs.Format">
            <summary>
            报告格式
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Interfaces.ReportGeneratedEventArgs.Size">
            <summary>
            报告大小（字节）
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Interfaces.ReportGeneratedEventArgs.Duration">
            <summary>
            生成耗时
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Interfaces.ReportGeneratedEventArgs.IsSuccess">
            <summary>
            是否成功
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Interfaces.ReportGeneratedEventArgs.ErrorMessage">
            <summary>
            错误信息
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Interfaces.ReportGeneratedEventArgs.Timestamp">
            <summary>
            时间戳
            </summary>
        </member>
        <member name="T:MassStorageStableTestTool.Core.Interfaces.IResultParser`1">
            <summary>
            结果解析器接口
            </summary>
            <typeparam name="T">测试结果类型</typeparam>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Interfaces.IResultParser`1.SupportedToolName">
            <summary>
            支持的工具名称
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Interfaces.IResultParser`1.SupportedFormats">
            <summary>
            支持的输出格式
            </summary>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Interfaces.IResultParser`1.ParseFromTextAsync(System.String,System.String)">
            <summary>
            从文本输出解析结果
            </summary>
            <param name="output">输出文本</param>
            <param name="format">输出格式</param>
            <returns>解析后的测试结果</returns>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Interfaces.IResultParser`1.ParseFromFileAsync(System.String,System.String)">
            <summary>
            从文件解析结果
            </summary>
            <param name="filePath">文件路径</param>
            <param name="format">文件格式</param>
            <returns>解析后的测试结果</returns>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Interfaces.IResultParser`1.ParseFromProcessResultAsync(MassStorageStableTestTool.Core.Models.ProcessResult)">
            <summary>
            从进程结果解析
            </summary>
            <param name="processResult">进程执行结果</param>
            <returns>解析后的测试结果</returns>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Interfaces.IResultParser`1.IsFormatSupported(System.String)">
            <summary>
            验证输出格式是否受支持
            </summary>
            <param name="format">输出格式</param>
            <returns>是否支持</returns>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Interfaces.IResultParser`1.CanParseResult(System.String)">
            <summary>
            检查是否可以解析指定工具的结果
            </summary>
            <param name="toolName">工具名称</param>
            <returns>是否可以解析</returns>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Interfaces.IResultParser`1.GetParserInfo">
            <summary>
            获取解析器信息
            </summary>
            <returns>解析器信息</returns>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Interfaces.IResultParser`1.ValidateOutput(System.String)">
            <summary>
            验证输出内容
            </summary>
            <param name="output">输出内容</param>
            <returns>验证结果</returns>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Interfaces.IResultParser`1.ExtractPerformanceMetricsAsync(System.String)">
            <summary>
            提取关键性能指标
            </summary>
            <param name="output">输出内容</param>
            <returns>性能指标</returns>
        </member>
        <member name="T:MassStorageStableTestTool.Core.Interfaces.IResultParser">
            <summary>
            通用结果解析器接口
            </summary>
        </member>
        <member name="T:MassStorageStableTestTool.Core.Interfaces.ParserInfo">
            <summary>
            解析器信息
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Interfaces.ParserInfo.Name">
            <summary>
            解析器名称
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Interfaces.ParserInfo.Version">
            <summary>
            解析器版本
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Interfaces.ParserInfo.SupportedTools">
            <summary>
            支持的工具列表
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Interfaces.ParserInfo.SupportedFormats">
            <summary>
            支持的格式列表
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Interfaces.ParserInfo.Description">
            <summary>
            解析器描述
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Interfaces.ParserInfo.Author">
            <summary>
            解析器作者
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Interfaces.ParserInfo.CreatedAt">
            <summary>
            创建时间
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Interfaces.ParserInfo.ParsingRules">
            <summary>
            解析规则
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Interfaces.ParserInfo.ExampleOutputs">
            <summary>
            示例输出
            </summary>
        </member>
        <member name="T:MassStorageStableTestTool.Core.Interfaces.IResultParserFactory">
            <summary>
            解析器工厂接口
            </summary>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Interfaces.IResultParserFactory.CreateParser(System.String)">
            <summary>
            创建解析器
            </summary>
            <param name="toolName">工具名称</param>
            <returns>解析器实例</returns>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Interfaces.IResultParserFactory.GetAllParsers">
            <summary>
            获取所有可用的解析器
            </summary>
            <returns>解析器列表</returns>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Interfaces.IResultParserFactory.RegisterParser(MassStorageStableTestTool.Core.Interfaces.IResultParser)">
            <summary>
            注册解析器
            </summary>
            <param name="parser">解析器实例</param>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Interfaces.IResultParserFactory.RegisterParser``1(System.String)">
            <summary>
            注册解析器类型
            </summary>
            <typeparam name="T">解析器类型</typeparam>
            <param name="toolName">工具名称</param>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Interfaces.IResultParserFactory.GetParsersForTool(System.String)">
            <summary>
            获取支持指定工具的解析器
            </summary>
            <param name="toolName">工具名称</param>
            <returns>解析器列表</returns>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Interfaces.IResultParserFactory.HasParserForTool(System.String)">
            <summary>
            检查是否有支持指定工具的解析器
            </summary>
            <param name="toolName">工具名称</param>
            <returns>是否有支持的解析器</returns>
        </member>
        <member name="T:MassStorageStableTestTool.Core.Interfaces.ParseContext">
            <summary>
            解析上下文
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Interfaces.ParseContext.ToolName">
            <summary>
            工具名称
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Interfaces.ParseContext.Format">
            <summary>
            输出格式
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Interfaces.ParseContext.Configuration">
            <summary>
            测试配置
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Interfaces.ParseContext.Parameters">
            <summary>
            额外参数
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Interfaces.ParseContext.Timestamp">
            <summary>
            时间戳
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Interfaces.ParseContext.Encoding">
            <summary>
            文件编码
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Interfaces.ParseContext.IgnoreErrors">
            <summary>
            是否忽略错误
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Interfaces.ParseContext.VerboseMode">
            <summary>
            详细模式
            </summary>
        </member>
        <member name="T:MassStorageStableTestTool.Core.Interfaces.ISystemInfoService">
            <summary>
            系统信息服务接口
            </summary>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Interfaces.ISystemInfoService.GetSystemInfoAsync">
            <summary>
            获取系统信息
            </summary>
            <returns>系统信息</returns>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Interfaces.ISystemInfoService.GetDriveInfoAsync(System.String)">
            <summary>
            获取驱动器信息
            </summary>
            <param name="driveName">驱动器名称</param>
            <returns>驱动器信息</returns>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Interfaces.ISystemInfoService.GetAvailableDrivesAsync">
            <summary>
            获取所有可用驱动器
            </summary>
            <returns>驱动器信息列表</returns>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Interfaces.ISystemInfoService.GetRemovableDrivesAsync">
            <summary>
            获取可移动驱动器列表
            </summary>
            <returns>可移动驱动器列表</returns>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Interfaces.ISystemInfoService.CheckDriveSuitabilityAsync(System.String,System.Int64)">
            <summary>
            检查驱动器是否适合测试
            </summary>
            <param name="driveName">驱动器名称</param>
            <param name="requiredSpace">所需空间（字节）</param>
            <returns>检查结果</returns>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Interfaces.ISystemInfoService.GetSystemPerformanceAsync">
            <summary>
            获取系统性能信息
            </summary>
            <returns>性能信息</returns>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Interfaces.ISystemInfoService.StartPerformanceMonitoringAsync(System.TimeSpan,System.Threading.CancellationToken)">
            <summary>
            监控系统性能
            </summary>
            <param name="interval">监控间隔</param>
            <param name="cancellationToken">取消令牌</param>
            <returns>性能监控任务</returns>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Interfaces.ISystemInfoService.StopPerformanceMonitoringAsync">
            <summary>
            停止性能监控
            </summary>
            <returns>停止结果</returns>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Interfaces.ISystemInfoService.GetInstalledSoftwareAsync">
            <summary>
            获取已安装的软件列表
            </summary>
            <returns>已安装软件列表</returns>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Interfaces.ISystemInfoService.IsSoftwareInstalledAsync(System.String)">
            <summary>
            检查软件是否已安装
            </summary>
            <param name="softwareName">软件名称</param>
            <returns>是否已安装</returns>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Interfaces.ISystemInfoService.GetEnvironmentVariablesAsync">
            <summary>
            获取环境变量
            </summary>
            <returns>环境变量字典</returns>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Interfaces.ISystemInfoService.GetNetworkAdaptersAsync">
            <summary>
            获取网络适配器信息
            </summary>
            <returns>网络适配器列表</returns>
        </member>
        <member name="E:MassStorageStableTestTool.Core.Interfaces.ISystemInfoService.PerformanceDataUpdated">
            <summary>
            性能数据更新事件
            </summary>
        </member>
        <member name="E:MassStorageStableTestTool.Core.Interfaces.ISystemInfoService.DriveStatusChanged">
            <summary>
            驱动器状态变化事件
            </summary>
        </member>
        <member name="T:MassStorageStableTestTool.Core.Interfaces.SystemPerformanceInfo">
            <summary>
            系统性能信息
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Interfaces.SystemPerformanceInfo.CpuUsage">
            <summary>
            CPU使用率 (%)
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Interfaces.SystemPerformanceInfo.MemoryUsage">
            <summary>
            内存使用率 (%)
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Interfaces.SystemPerformanceInfo.DiskUsage">
            <summary>
            磁盘使用率 (%)
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Interfaces.SystemPerformanceInfo.NetworkUsage">
            <summary>
            网络使用率 (%)
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Interfaces.SystemPerformanceInfo.AvailableMemory">
            <summary>
            可用内存 (GB)
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Interfaces.SystemPerformanceInfo.TotalMemory">
            <summary>
            总内存 (GB)
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Interfaces.SystemPerformanceInfo.SystemTemperature">
            <summary>
            系统温度 (°C)
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Interfaces.SystemPerformanceInfo.ProcessCount">
            <summary>
            运行的进程数
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Interfaces.SystemPerformanceInfo.Uptime">
            <summary>
            系统运行时间
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Interfaces.SystemPerformanceInfo.Timestamp">
            <summary>
            时间戳
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Interfaces.SystemPerformanceInfo.CpuDetails">
            <summary>
            详细的CPU信息
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Interfaces.SystemPerformanceInfo.MemoryDetails">
            <summary>
            详细的内存信息
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Interfaces.SystemPerformanceInfo.DiskDetails">
            <summary>
            详细的磁盘信息
            </summary>
        </member>
        <member name="T:MassStorageStableTestTool.Core.Interfaces.PerformanceDataEventArgs">
            <summary>
            性能数据事件参数
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Interfaces.PerformanceDataEventArgs.PerformanceInfo">
            <summary>
            性能信息
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Interfaces.PerformanceDataEventArgs.Timestamp">
            <summary>
            时间戳
            </summary>
        </member>
        <member name="T:MassStorageStableTestTool.Core.Interfaces.DriveStatusChangedEventArgs">
            <summary>
            驱动器状态变化事件参数
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Interfaces.DriveStatusChangedEventArgs.DriveName">
            <summary>
            驱动器名称
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Interfaces.DriveStatusChangedEventArgs.ChangeType">
            <summary>
            变化类型
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Interfaces.DriveStatusChangedEventArgs.DriveInfo">
            <summary>
            驱动器信息
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Interfaces.DriveStatusChangedEventArgs.Timestamp">
            <summary>
            时间戳
            </summary>
        </member>
        <member name="T:MassStorageStableTestTool.Core.Interfaces.DriveChangeType">
            <summary>
            驱动器变化类型
            </summary>
        </member>
        <member name="F:MassStorageStableTestTool.Core.Interfaces.DriveChangeType.Inserted">
            <summary>
            插入
            </summary>
        </member>
        <member name="F:MassStorageStableTestTool.Core.Interfaces.DriveChangeType.Removed">
            <summary>
            移除
            </summary>
        </member>
        <member name="F:MassStorageStableTestTool.Core.Interfaces.DriveChangeType.StatusChanged">
            <summary>
            状态变化
            </summary>
        </member>
        <member name="F:MassStorageStableTestTool.Core.Interfaces.DriveChangeType.SpaceChanged">
            <summary>
            空间变化
            </summary>
        </member>
        <member name="T:MassStorageStableTestTool.Core.Interfaces.ITestOrchestrator">
            <summary>
            测试编排器接口
            </summary>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Interfaces.ITestOrchestrator.ExecuteTestSuiteAsync(MassStorageStableTestTool.Core.Models.TestConfiguration,System.Threading.CancellationToken,System.IProgress{MassStorageStableTestTool.Core.Interfaces.ProgressEventArgs})">
            <summary>
            执行测试套件
            </summary>
            <param name="configuration">测试配置</param>
            <param name="cancellationToken">取消令牌</param>
            <param name="progress">进度报告器</param>
            <returns>测试套件结果</returns>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Interfaces.ITestOrchestrator.GetAvailableToolsAsync">
            <summary>
            获取可用的测试工具列表
            </summary>
            <returns>可用的测试工具</returns>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Interfaces.ITestOrchestrator.GetToolController(System.String)">
            <summary>
            根据名称获取测试工具控制器
            </summary>
            <param name="toolName">工具名称</param>
            <returns>测试工具控制器</returns>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Interfaces.ITestOrchestrator.ValidateConfigurationAsync(MassStorageStableTestTool.Core.Models.TestConfiguration)">
            <summary>
            验证测试配置
            </summary>
            <param name="configuration">测试配置</param>
            <returns>验证结果</returns>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Interfaces.ITestOrchestrator.EstimateTestDurationAsync(MassStorageStableTestTool.Core.Models.TestConfiguration)">
            <summary>
            估算测试时间
            </summary>
            <param name="configuration">测试配置</param>
            <returns>估算的测试时间</returns>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Interfaces.ITestOrchestrator.StopCurrentTestSuiteAsync">
            <summary>
            停止当前运行的测试套件
            </summary>
            <returns>停止结果</returns>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Interfaces.ITestOrchestrator.GetCurrentStatus">
            <summary>
            获取当前测试套件状态
            </summary>
            <returns>当前状态</returns>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Interfaces.ITestOrchestrator.PauseCurrentTestSuiteAsync">
            <summary>
            暂停当前测试套件
            </summary>
            <returns>暂停结果</returns>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Interfaces.ITestOrchestrator.ResumeCurrentTestSuiteAsync">
            <summary>
            恢复当前测试套件
            </summary>
            <returns>恢复结果</returns>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Interfaces.ITestOrchestrator.GetTestHistoryAsync(System.Int32)">
            <summary>
            获取测试历史记录
            </summary>
            <param name="count">记录数量</param>
            <returns>历史记录列表</returns>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Interfaces.ITestOrchestrator.CleanupTestEnvironmentAsync(MassStorageStableTestTool.Core.Models.TestConfiguration,System.Threading.CancellationToken)">
            <summary>
            清理测试环境
            </summary>
            <param name="configuration">测试配置</param>
            <param name="cancellationToken">取消令牌</param>
            <returns>清理结果</returns>
        </member>
        <member name="E:MassStorageStableTestTool.Core.Interfaces.ITestOrchestrator.StatusChanged">
            <summary>
            测试套件状态变化事件
            </summary>
        </member>
        <member name="E:MassStorageStableTestTool.Core.Interfaces.ITestOrchestrator.ToolStatusChanged">
            <summary>
            测试工具状态变化事件
            </summary>
        </member>
        <member name="E:MassStorageStableTestTool.Core.Interfaces.ITestOrchestrator.LogReceived">
            <summary>
            日志事件
            </summary>
        </member>
        <member name="T:MassStorageStableTestTool.Core.Interfaces.TestSuiteStatus">
            <summary>
            测试套件状态枚举
            </summary>
        </member>
        <member name="F:MassStorageStableTestTool.Core.Interfaces.TestSuiteStatus.Idle">
            <summary>
            空闲
            </summary>
        </member>
        <member name="F:MassStorageStableTestTool.Core.Interfaces.TestSuiteStatus.Preparing">
            <summary>
            准备中
            </summary>
        </member>
        <member name="F:MassStorageStableTestTool.Core.Interfaces.TestSuiteStatus.Running">
            <summary>
            运行中
            </summary>
        </member>
        <member name="F:MassStorageStableTestTool.Core.Interfaces.TestSuiteStatus.Paused">
            <summary>
            暂停
            </summary>
        </member>
        <member name="F:MassStorageStableTestTool.Core.Interfaces.TestSuiteStatus.Completed">
            <summary>
            完成
            </summary>
        </member>
        <member name="F:MassStorageStableTestTool.Core.Interfaces.TestSuiteStatus.Failed">
            <summary>
            失败
            </summary>
        </member>
        <member name="F:MassStorageStableTestTool.Core.Interfaces.TestSuiteStatus.Cancelled">
            <summary>
            已取消
            </summary>
        </member>
        <member name="F:MassStorageStableTestTool.Core.Interfaces.TestSuiteStatus.Cleaning">
            <summary>
            清理中
            </summary>
        </member>
        <member name="T:MassStorageStableTestTool.Core.Interfaces.TestSuiteStatusChangedEventArgs">
            <summary>
            测试套件状态变化事件参数
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Interfaces.TestSuiteStatusChangedEventArgs.OldStatus">
            <summary>
            旧状态
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Interfaces.TestSuiteStatusChangedEventArgs.NewStatus">
            <summary>
            新状态
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Interfaces.TestSuiteStatusChangedEventArgs.Message">
            <summary>
            状态消息
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Interfaces.TestSuiteStatusChangedEventArgs.CurrentTool">
            <summary>
            当前测试工具
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Interfaces.TestSuiteStatusChangedEventArgs.CompletedTests">
            <summary>
            已完成的测试数量
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Interfaces.TestSuiteStatusChangedEventArgs.TotalTests">
            <summary>
            总测试数量
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Interfaces.TestSuiteStatusChangedEventArgs.OverallProgress">
            <summary>
            总体进度百分比
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Interfaces.TestSuiteStatusChangedEventArgs.Timestamp">
            <summary>
            时间戳
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Interfaces.TestSuiteStatusChangedEventArgs.Data">
            <summary>
            额外数据
            </summary>
        </member>
        <member name="T:MassStorageStableTestTool.Core.Interfaces.ITestToolController">
            <summary>
            测试工具控制器接口
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Interfaces.ITestToolController.ToolName">
            <summary>
            工具名称
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Interfaces.ITestToolController.ToolType">
            <summary>
            工具类型
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Interfaces.ITestToolController.Configuration">
            <summary>
            工具配置
            </summary>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Interfaces.ITestToolController.ExecuteTestAsync(MassStorageStableTestTool.Core.Models.TestConfiguration,System.Threading.CancellationToken,System.IProgress{MassStorageStableTestTool.Core.Interfaces.ProgressEventArgs})">
            <summary>
            执行测试
            </summary>
            <param name="config">测试配置</param>
            <param name="cancellationToken">取消令牌</param>
            <param name="progress">进度报告器</param>
            <returns>测试结果</returns>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Interfaces.ITestToolController.IsToolAvailable">
            <summary>
            检查工具是否可用
            </summary>
            <returns>是否可用</returns>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Interfaces.ITestToolController.GetToolVersionAsync">
            <summary>
            获取工具版本
            </summary>
            <returns>工具版本</returns>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Interfaces.ITestToolController.ValidateConfiguration(MassStorageStableTestTool.Core.Models.TestConfiguration)">
            <summary>
            验证测试配置
            </summary>
            <param name="config">测试配置</param>
            <returns>验证结果</returns>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Interfaces.ITestToolController.PrepareTestEnvironmentAsync(MassStorageStableTestTool.Core.Models.TestConfiguration,System.Threading.CancellationToken)">
            <summary>
            准备测试环境
            </summary>
            <param name="config">测试配置</param>
            <param name="cancellationToken">取消令牌</param>
            <returns>准备结果</returns>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Interfaces.ITestToolController.CleanupTestEnvironmentAsync(MassStorageStableTestTool.Core.Models.TestConfiguration,System.Threading.CancellationToken)">
            <summary>
            清理测试环境
            </summary>
            <param name="config">测试配置</param>
            <param name="cancellationToken">取消令牌</param>
            <returns>清理结果</returns>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Interfaces.ITestToolController.StopTestAsync">
            <summary>
            停止正在运行的测试
            </summary>
            <returns>停止结果</returns>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Interfaces.ITestToolController.GetCurrentStatus">
            <summary>
            获取当前测试状态
            </summary>
            <returns>测试状态</returns>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Interfaces.ITestToolController.GetSupportedParameters">
            <summary>
            获取支持的参数列表
            </summary>
            <returns>支持的参数</returns>
        </member>
        <member name="E:MassStorageStableTestTool.Core.Interfaces.ITestToolController.StatusChanged">
            <summary>
            状态变化事件
            </summary>
        </member>
        <member name="E:MassStorageStableTestTool.Core.Interfaces.ITestToolController.LogReceived">
            <summary>
            日志事件
            </summary>
        </member>
        <member name="T:MassStorageStableTestTool.Core.Interfaces.ProgressEventArgs">
            <summary>
            进度事件参数
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Interfaces.ProgressEventArgs.Progress">
            <summary>
            进度百分比 (0-100)
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Interfaces.ProgressEventArgs.Status">
            <summary>
            状态消息
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Interfaces.ProgressEventArgs.Phase">
            <summary>
            当前阶段
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Interfaces.ProgressEventArgs.Details">
            <summary>
            详细信息
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Interfaces.ProgressEventArgs.Timestamp">
            <summary>
            时间戳
            </summary>
        </member>
        <member name="T:MassStorageStableTestTool.Core.Interfaces.TestStatusChangedEventArgs">
            <summary>
            测试状态变化事件参数
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Interfaces.TestStatusChangedEventArgs.ToolName">
            <summary>
            工具名称
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Interfaces.TestStatusChangedEventArgs.OldStatus">
            <summary>
            旧状态
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Interfaces.TestStatusChangedEventArgs.NewStatus">
            <summary>
            新状态
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Interfaces.TestStatusChangedEventArgs.Message">
            <summary>
            状态消息
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Interfaces.TestStatusChangedEventArgs.Timestamp">
            <summary>
            时间戳
            </summary>
        </member>
        <member name="T:MassStorageStableTestTool.Core.Interfaces.LogEventArgs">
            <summary>
            日志事件参数
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Interfaces.LogEventArgs.Level">
            <summary>
            日志级别
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Interfaces.LogEventArgs.Message">
            <summary>
            日志消息
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Interfaces.LogEventArgs.Exception">
            <summary>
            异常信息
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Interfaces.LogEventArgs.Timestamp">
            <summary>
            时间戳
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Interfaces.LogEventArgs.Source">
            <summary>
            日志来源
            </summary>
        </member>
        <member name="T:MassStorageStableTestTool.Core.Interfaces.ParameterInfo">
            <summary>
            参数信息
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Interfaces.ParameterInfo.Name">
            <summary>
            参数名称
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Interfaces.ParameterInfo.Type">
            <summary>
            参数类型
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Interfaces.ParameterInfo.DefaultValue">
            <summary>
            默认值
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Interfaces.ParameterInfo.Description">
            <summary>
            参数描述
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Interfaces.ParameterInfo.IsRequired">
            <summary>
            是否必需
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Interfaces.ParameterInfo.PossibleValues">
            <summary>
            可选值列表
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Interfaces.ParameterInfo.MinValue">
            <summary>
            最小值
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Interfaces.ParameterInfo.MaxValue">
            <summary>
            最大值
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Interfaces.ParameterInfo.ValidationPattern">
            <summary>
            验证规则（正则表达式）
            </summary>
        </member>
        <member name="T:MassStorageStableTestTool.Core.Interfaces.LogLevel">
            <summary>
            日志级别枚举
            </summary>
        </member>
        <member name="F:MassStorageStableTestTool.Core.Interfaces.LogLevel.Debug">
            <summary>
            调试
            </summary>
        </member>
        <member name="F:MassStorageStableTestTool.Core.Interfaces.LogLevel.Information">
            <summary>
            信息
            </summary>
        </member>
        <member name="F:MassStorageStableTestTool.Core.Interfaces.LogLevel.Warning">
            <summary>
            警告
            </summary>
        </member>
        <member name="F:MassStorageStableTestTool.Core.Interfaces.LogLevel.Error">
            <summary>
            错误
            </summary>
        </member>
        <member name="F:MassStorageStableTestTool.Core.Interfaces.LogLevel.Critical">
            <summary>
            严重错误
            </summary>
        </member>
        <member name="T:MassStorageStableTestTool.Core.Models.ApplicationConfiguration">
            <summary>
            应用程序配置模型
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.ApplicationConfiguration.TestTools">
            <summary>
            测试工具配置字典
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.ApplicationConfiguration.Reporting">
            <summary>
            报告配置
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.ApplicationConfiguration.Logging">
            <summary>
            日志配置
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.ApplicationConfiguration.Performance">
            <summary>
            性能配置
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.ApplicationConfiguration.UI">
            <summary>
            UI配置
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.ApplicationConfiguration.Security">
            <summary>
            安全配置
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.ApplicationConfiguration.Version">
            <summary>
            配置版本
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.ApplicationConfiguration.CreatedAt">
            <summary>
            创建时间
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.ApplicationConfiguration.LastModified">
            <summary>
            最后修改时间
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.ApplicationConfiguration.Description">
            <summary>
            配置描述
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.ApplicationConfiguration.CustomSettings">
            <summary>
            自定义设置
            </summary>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Models.ApplicationConfiguration.Validate">
            <summary>
            验证配置有效性
            </summary>
            <returns>验证结果</returns>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Models.ApplicationConfiguration.Clone">
            <summary>
            克隆配置
            </summary>
            <returns>配置副本</returns>
        </member>
        <member name="T:MassStorageStableTestTool.Core.Models.ReportConfiguration">
            <summary>
            报告配置
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.ReportConfiguration.OutputDirectory">
            <summary>
            输出目录
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.ReportConfiguration.FileNameTemplate">
            <summary>
            文件名模板
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.ReportConfiguration.IncludeSystemInfo">
            <summary>
            是否包含系统信息
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.ReportConfiguration.Formats">
            <summary>
            支持的格式
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.ReportConfiguration.AutoOpenReport">
            <summary>
            自动打开报告
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.ReportConfiguration.TemplatePath">
            <summary>
            报告模板路径
            </summary>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Models.ReportConfiguration.Clone">
            <summary>
            克隆配置
            </summary>
            <returns>配置副本</returns>
        </member>
        <member name="T:MassStorageStableTestTool.Core.Models.LoggingConfiguration">
            <summary>
            日志配置
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.LoggingConfiguration.LogLevel">
            <summary>
            日志级别
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.LoggingConfiguration.LogDirectory">
            <summary>
            日志目录
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.LoggingConfiguration.EnableConsoleOutput">
            <summary>
            是否启用控制台输出
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.LoggingConfiguration.EnableFileOutput">
            <summary>
            是否启用文件输出
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.LoggingConfiguration.MaxFileSizeMB">
            <summary>
            日志文件最大大小（MB）
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.LoggingConfiguration.RetainedFileCount">
            <summary>
            保留的日志文件数量
            </summary>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Models.LoggingConfiguration.Clone">
            <summary>
            克隆配置
            </summary>
            <returns>配置副本</returns>
        </member>
        <member name="T:MassStorageStableTestTool.Core.Models.PerformanceConfiguration">
            <summary>
            性能配置
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.PerformanceConfiguration.MaxConcurrentTests">
            <summary>
            最大并发测试数
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.PerformanceConfiguration.ProcessCleanupTimeout">
            <summary>
            进程清理超时时间（秒）
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.PerformanceConfiguration.MemoryThreshold">
            <summary>
            内存阈值（百分比）
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.PerformanceConfiguration.EnablePerformanceMonitoring">
            <summary>
            是否启用性能监控
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.PerformanceConfiguration.MonitoringInterval">
            <summary>
            监控间隔（秒）
            </summary>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Models.PerformanceConfiguration.Clone">
            <summary>
            克隆配置
            </summary>
            <returns>配置副本</returns>
        </member>
        <member name="T:MassStorageStableTestTool.Core.Models.UIConfiguration">
            <summary>
            UI配置
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.UIConfiguration.Theme">
            <summary>
            主题
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.UIConfiguration.Language">
            <summary>
            语言
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.UIConfiguration.WindowSize">
            <summary>
            窗口大小
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.UIConfiguration.AutoSaveWindowState">
            <summary>
            是否自动保存窗口状态
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.UIConfiguration.RefreshInterval">
            <summary>
            刷新间隔（毫秒）
            </summary>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Models.UIConfiguration.Clone">
            <summary>
            克隆配置
            </summary>
            <returns>配置副本</returns>
        </member>
        <member name="T:MassStorageStableTestTool.Core.Models.WindowSize">
            <summary>
            窗口大小配置
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.WindowSize.Width">
            <summary>
            宽度
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.WindowSize.Height">
            <summary>
            高度
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.WindowSize.IsMaximized">
            <summary>
            是否最大化
            </summary>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Models.WindowSize.Clone">
            <summary>
            克隆配置
            </summary>
            <returns>配置副本</returns>
        </member>
        <member name="T:MassStorageStableTestTool.Core.Models.SecurityConfiguration">
            <summary>
            安全配置
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.SecurityConfiguration.AllowedExecutableDirectories">
            <summary>
            允许的可执行文件目录
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.SecurityConfiguration.VerifyDigitalSignature">
            <summary>
            是否验证数字签名
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.SecurityConfiguration.RestrictDriveAccess">
            <summary>
            是否限制驱动器访问
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.SecurityConfiguration.AllowedDriveTypes">
            <summary>
            允许的驱动器类型
            </summary>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Models.SecurityConfiguration.Clone">
            <summary>
            克隆配置
            </summary>
            <returns>配置副本</returns>
        </member>
        <member name="T:MassStorageStableTestTool.Core.Models.DriveInfo">
            <summary>
            驱动器信息模型
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.DriveInfo.Name">
            <summary>
            驱动器名称（如 "E:", "F:" 等）
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.DriveInfo.Label">
            <summary>
            驱动器标签
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.DriveInfo.DriveType">
            <summary>
            驱动器类型
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.DriveInfo.FileSystem">
            <summary>
            文件系统类型
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.DriveInfo.TotalSize">
            <summary>
            总容量（字节）
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.DriveInfo.AvailableFreeSpace">
            <summary>
            可用空间（字节）
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.DriveInfo.TotalFreeSpace">
            <summary>
            总可用空间（字节）
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.DriveInfo.UsedSpace">
            <summary>
            已使用空间（字节）
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.DriveInfo.UsagePercentage">
            <summary>
            使用率百分比
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.DriveInfo.IsReady">
            <summary>
            是否就绪
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.DriveInfo.RootDirectory">
            <summary>
            根目录
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.DriveInfo.DriveFormat">
            <summary>
            驱动器格式
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.DriveInfo.SerialNumber">
            <summary>
            序列号
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.DriveInfo.Manufacturer">
            <summary>
            制造商
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.DriveInfo.Model">
            <summary>
            型号
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.DriveInfo.InterfaceType">
            <summary>
            接口类型（如 USB 3.0, SATA 等）
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.DriveInfo.IsRemovable">
            <summary>
            是否可移动
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.DriveInfo.HealthStatus">
            <summary>
            健康状态
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.DriveInfo.Temperature">
            <summary>
            温度（摄氏度）
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.DriveInfo.ReadSpeed">
            <summary>
            读取速度（MB/s）
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.DriveInfo.WriteSpeed">
            <summary>
            写入速度（MB/s）
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.DriveInfo.CreatedAt">
            <summary>
            创建时间
            </summary>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Models.DriveInfo.GetFormattedTotalSize">
            <summary>
            获取总容量（格式化字符串）
            </summary>
            <returns>格式化的容量字符串</returns>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Models.DriveInfo.GetFormattedFreeSpace">
            <summary>
            获取可用空间（格式化字符串）
            </summary>
            <returns>格式化的可用空间字符串</returns>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Models.DriveInfo.GetFormattedUsedSpace">
            <summary>
            获取已使用空间（格式化字符串）
            </summary>
            <returns>格式化的已使用空间字符串</returns>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Models.DriveInfo.FormatBytes(System.Int64)">
            <summary>
            格式化字节数
            </summary>
            <param name="bytes">字节数</param>
            <returns>格式化字符串</returns>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Models.DriveInfo.CheckSuitabilityForTesting(System.Int64)">
            <summary>
            检查是否适合测试
            </summary>
            <param name="requiredSpace">所需空间（字节）</param>
            <returns>检查结果</returns>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Models.DriveInfo.ToDictionary">
            <summary>
            转换为字典格式
            </summary>
            <returns>驱动器信息字典</returns>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Models.DriveInfo.GetSummary">
            <summary>
            获取驱动器摘要信息
            </summary>
            <returns>驱动器摘要</returns>
        </member>
        <member name="T:MassStorageStableTestTool.Core.Models.DriveHealthStatus">
            <summary>
            驱动器健康状态枚举
            </summary>
        </member>
        <member name="F:MassStorageStableTestTool.Core.Models.DriveHealthStatus.Unknown">
            <summary>
            未知
            </summary>
        </member>
        <member name="F:MassStorageStableTestTool.Core.Models.DriveHealthStatus.Healthy">
            <summary>
            健康
            </summary>
        </member>
        <member name="F:MassStorageStableTestTool.Core.Models.DriveHealthStatus.Warning">
            <summary>
            警告
            </summary>
        </member>
        <member name="F:MassStorageStableTestTool.Core.Models.DriveHealthStatus.Critical">
            <summary>
            严重
            </summary>
        </member>
        <member name="T:MassStorageStableTestTool.Core.Models.PerformanceMetrics">
            <summary>
            性能指标模型
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.PerformanceMetrics.ReadSpeed">
            <summary>
            读取速度 (MB/s)
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.PerformanceMetrics.WriteSpeed">
            <summary>
            写入速度 (MB/s)
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.PerformanceMetrics.ReadIOPS">
            <summary>
            读取IOPS
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.PerformanceMetrics.WriteIOPS">
            <summary>
            写入IOPS
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.PerformanceMetrics.ReadLatency">
            <summary>
            读取延迟 (ms)
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.PerformanceMetrics.WriteLatency">
            <summary>
            写入延迟 (ms)
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.PerformanceMetrics.AverageLatency">
            <summary>
            平均延迟 (ms)
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.PerformanceMetrics.MinLatency">
            <summary>
            最小延迟 (ms)
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.PerformanceMetrics.MaxLatency">
            <summary>
            最大延迟 (ms)
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.PerformanceMetrics.TotalBandwidth">
            <summary>
            总带宽 (MB/s)
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.PerformanceMetrics.TotalIOPS">
            <summary>
            总IOPS
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.PerformanceMetrics.DataVolume">
            <summary>
            测试的数据量 (MB)
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.PerformanceMetrics.ErrorCount">
            <summary>
            错误数量
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.PerformanceMetrics.CpuUsage">
            <summary>
            CPU使用率 (%)
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.PerformanceMetrics.MemoryUsage">
            <summary>
            内存使用率 (%)
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.PerformanceMetrics.QueueDepth">
            <summary>
            队列深度
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.PerformanceMetrics.BlockSize">
            <summary>
            块大小 (KB)
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.PerformanceMetrics.ThreadCount">
            <summary>
            线程数
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.PerformanceMetrics.RandomAccessPercentage">
            <summary>
            随机访问百分比
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.PerformanceMetrics.ReadPercentage">
            <summary>
            读取百分比
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.PerformanceMetrics.WritePercentage">
            <summary>
            写入百分比
            </summary>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Models.PerformanceMetrics.GetOverallScore">
            <summary>
            获取综合性能评分
            </summary>
            <returns>性能评分 (0-100)</returns>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Models.PerformanceMetrics.GetPerformanceGrade">
            <summary>
            获取性能等级
            </summary>
            <returns>性能等级</returns>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Models.PerformanceMetrics.ToDictionary">
            <summary>
            转换为字典格式
            </summary>
            <returns>性能指标字典</returns>
        </member>
        <member name="T:MassStorageStableTestTool.Core.Models.ProcessResult">
            <summary>
            进程执行结果模型
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.ProcessResult.ExitCode">
            <summary>
            进程退出码
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.ProcessResult.StandardOutput">
            <summary>
            标准输出
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.ProcessResult.StandardError">
            <summary>
            标准错误输出
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.ProcessResult.StartTime">
            <summary>
            进程开始时间
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.ProcessResult.EndTime">
            <summary>
            进程结束时间
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.ProcessResult.Duration">
            <summary>
            进程运行时间
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.ProcessResult.ProcessId">
            <summary>
            进程ID
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.ProcessResult.ProcessName">
            <summary>
            进程名称
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.ProcessResult.Arguments">
            <summary>
            命令行参数
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.ProcessResult.WorkingDirectory">
            <summary>
            工作目录
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.ProcessResult.IsSuccess">
            <summary>
            是否成功执行（退出码为0）
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.ProcessResult.HasError">
            <summary>
            是否有错误输出
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.ProcessResult.HasOutput">
            <summary>
            是否有标准输出
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.ProcessResult.EnvironmentVariables">
            <summary>
            环境变量
            </summary>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Models.ProcessResult.GetAllOutput">
            <summary>
            获取所有输出（标准输出 + 错误输出）
            </summary>
            <returns>合并的输出</returns>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Models.ProcessResult.GetOutputLines(System.Boolean)">
            <summary>
            获取输出行列表
            </summary>
            <param name="includeEmpty">是否包含空行</param>
            <returns>输出行列表</returns>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Models.ProcessResult.GetErrorLines(System.Boolean)">
            <summary>
            获取错误行列表
            </summary>
            <param name="includeEmpty">是否包含空行</param>
            <returns>错误行列表</returns>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Models.ProcessResult.SearchOutput(System.String,System.Boolean)">
            <summary>
            搜索输出中的特定模式
            </summary>
            <param name="pattern">搜索模式（正则表达式）</param>
            <param name="searchInError">是否也在错误输出中搜索</param>
            <returns>匹配的行列表</returns>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Models.ProcessResult.ToDictionary">
            <summary>
            转换为字典格式
            </summary>
            <returns>进程结果字典</returns>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Models.ProcessResult.Success(System.String,System.String)">
            <summary>
            创建成功的进程结果
            </summary>
            <param name="output">标准输出</param>
            <param name="processName">进程名称</param>
            <returns>成功的进程结果</returns>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Models.ProcessResult.Failure(System.Int32,System.String,System.String)">
            <summary>
            创建失败的进程结果
            </summary>
            <param name="exitCode">退出码</param>
            <param name="error">错误信息</param>
            <param name="processName">进程名称</param>
            <returns>失败的进程结果</returns>
        </member>
        <member name="T:MassStorageStableTestTool.Core.Models.SystemInfo">
            <summary>
            系统信息模型
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.SystemInfo.OperatingSystem">
            <summary>
            操作系统名称
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.SystemInfo.OSVersion">
            <summary>
            操作系统版本
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.SystemInfo.Architecture">
            <summary>
            操作系统架构
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.SystemInfo.Processor">
            <summary>
            处理器名称
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.SystemInfo.ProcessorCores">
            <summary>
            处理器核心数
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.SystemInfo.LogicalProcessors">
            <summary>
            处理器逻辑核心数
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.SystemInfo.TotalMemory">
            <summary>
            总内存大小（GB）
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.SystemInfo.AvailableMemory">
            <summary>
            可用内存大小（GB）
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.SystemInfo.ComputerName">
            <summary>
            计算机名称
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.SystemInfo.UserName">
            <summary>
            用户名
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.SystemInfo.SystemStartTime">
            <summary>
            系统启动时间
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.SystemInfo.DotNetVersion">
            <summary>
            .NET 运行时版本
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.SystemInfo.SystemLanguage">
            <summary>
            系统语言
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.SystemInfo.TimeZone">
            <summary>
            时区
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.SystemInfo.ScreenResolution">
            <summary>
            屏幕分辨率
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.SystemInfo.SystemUptime">
            <summary>
            系统运行时间
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.SystemInfo.MemoryUsagePercentage">
            <summary>
            内存使用率
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.SystemInfo.EnvironmentVariables">
            <summary>
            环境变量
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.SystemInfo.InstalledSoftware">
            <summary>
            已安装的软件列表
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.SystemInfo.NetworkAdapters">
            <summary>
            网络适配器信息
            </summary>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Models.SystemInfo.ToDictionary">
            <summary>
            转换为字典格式
            </summary>
            <returns>系统信息字典</returns>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Models.SystemInfo.GetSummary">
            <summary>
            获取系统摘要信息
            </summary>
            <returns>系统摘要</returns>
        </member>
        <member name="T:MassStorageStableTestTool.Core.Models.InstalledSoftware">
            <summary>
            已安装软件信息
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.InstalledSoftware.Name">
            <summary>
            软件名称
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.InstalledSoftware.Version">
            <summary>
            软件版本
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.InstalledSoftware.Publisher">
            <summary>
            发布商
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.InstalledSoftware.InstallDate">
            <summary>
            安装日期
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.InstalledSoftware.InstallPath">
            <summary>
            安装路径
            </summary>
        </member>
        <member name="T:MassStorageStableTestTool.Core.Models.NetworkAdapter">
            <summary>
            网络适配器信息
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.NetworkAdapter.Name">
            <summary>
            适配器名称
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.NetworkAdapter.Description">
            <summary>
            适配器描述
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.NetworkAdapter.MacAddress">
            <summary>
            MAC地址
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.NetworkAdapter.IPAddresses">
            <summary>
            IP地址列表
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.NetworkAdapter.IsEnabled">
            <summary>
            是否启用
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.NetworkAdapter.Speed">
            <summary>
            连接速度（Mbps）
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.NetworkAdapter.AdapterType">
            <summary>
            适配器类型
            </summary>
        </member>
        <member name="T:MassStorageStableTestTool.Core.Models.TestConfiguration">
            <summary>
            测试配置模型
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.TestConfiguration.TargetDrive">
            <summary>
            目标驱动器（如 "E:", "F:" 等）
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.TestConfiguration.SelectedTools">
            <summary>
            选中的测试工具列表
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.TestConfiguration.Parameters">
            <summary>
            测试参数字典
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.TestConfiguration.TestSize">
            <summary>
            测试大小（如 "1GB", "500MB" 等）
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.TestConfiguration.TestCount">
            <summary>
            测试次数
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.TestConfiguration.TimeoutSeconds">
            <summary>
            测试超时时间（秒）
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.TestConfiguration.VerifyData">
            <summary>
            是否验证数据完整性
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.TestConfiguration.TestAllSpace">
            <summary>
            是否测试全部可用空间
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.TestConfiguration.BlockSize">
            <summary>
            块大小（如 "4K", "64K" 等）
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.TestConfiguration.ReadWriteRatio">
            <summary>
            读写比例（如 "randrw", "read", "write" 等）
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.TestConfiguration.ThreadCount">
            <summary>
            线程数
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.TestConfiguration.VerboseLogging">
            <summary>
            是否启用详细日志
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.TestConfiguration.OutputDirectory">
            <summary>
            自定义输出目录
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.TestConfiguration.ConfigurationName">
            <summary>
            配置名称
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.TestConfiguration.Description">
            <summary>
            配置描述
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.TestConfiguration.CreatedAt">
            <summary>
            创建时间
            </summary>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Models.TestConfiguration.GetToolParameters(System.String)">
            <summary>
            获取指定工具的参数
            </summary>
            <param name="toolName">工具名称</param>
            <returns>工具参数字典</returns>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Models.TestConfiguration.SetToolParameter(System.String,System.String,System.Object)">
            <summary>
            设置工具参数
            </summary>
            <param name="toolName">工具名称</param>
            <param name="parameterName">参数名称</param>
            <param name="value">参数值</param>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Models.TestConfiguration.Validate">
            <summary>
            验证配置有效性
            </summary>
            <returns>验证结果</returns>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Models.TestConfiguration.Clone">
            <summary>
            克隆配置
            </summary>
            <returns>配置副本</returns>
        </member>
        <member name="T:MassStorageStableTestTool.Core.Models.TestResult">
            <summary>
            测试结果模型
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.TestResult.ToolName">
            <summary>
            工具名称
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.TestResult.Success">
            <summary>
            测试是否成功
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.TestResult.Status">
            <summary>
            测试状态
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.TestResult.StartTime">
            <summary>
            开始时间
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.TestResult.EndTime">
            <summary>
            结束时间
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.TestResult.Duration">
            <summary>
            测试持续时间
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.TestResult.Data">
            <summary>
            测试数据字典
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.TestResult.ErrorMessage">
            <summary>
            错误信息
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.TestResult.Exception">
            <summary>
            异常详情
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.TestResult.Warnings">
            <summary>
            警告信息列表
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.TestResult.Logs">
            <summary>
            详细日志
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.TestResult.OutputFiles">
            <summary>
            输出文件路径列表
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.TestResult.Performance">
            <summary>
            性能指标
            </summary>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Models.TestResult.AddLog(System.String)">
            <summary>
            添加日志条目
            </summary>
            <param name="message">日志消息</param>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Models.TestResult.AddWarning(System.String)">
            <summary>
            添加警告
            </summary>
            <param name="warning">警告信息</param>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Models.TestResult.SetPerformanceData(System.String,System.Object)">
            <summary>
            设置性能数据
            </summary>
            <param name="key">数据键</param>
            <param name="value">数据值</param>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Models.TestResult.GetPerformanceData``1(System.String)">
            <summary>
            获取性能数据
            </summary>
            <typeparam name="T">数据类型</typeparam>
            <param name="key">数据键</param>
            <returns>数据值</returns>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Models.TestResult.Complete(System.Boolean,System.String)">
            <summary>
            标记测试完成
            </summary>
            <param name="success">是否成功</param>
            <param name="errorMessage">错误信息（如果失败）</param>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Models.TestResult.Cancel">
            <summary>
            标记测试取消
            </summary>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Models.TestResult.Timeout">
            <summary>
            标记测试超时
            </summary>
        </member>
        <member name="T:MassStorageStableTestTool.Core.Models.TestSuiteResult">
            <summary>
            测试套件结果模型
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.TestSuiteResult.Configuration">
            <summary>
            测试配置
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.TestSuiteResult.TestResults">
            <summary>
            测试结果列表
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.TestSuiteResult.StartTime">
            <summary>
            开始时间
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.TestSuiteResult.EndTime">
            <summary>
            结束时间
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.TestSuiteResult.Duration">
            <summary>
            总持续时间
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.TestSuiteResult.Status">
            <summary>
            套件状态
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.TestSuiteResult.AllTestsPassed">
            <summary>
            是否所有测试都通过
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.TestSuiteResult.SuccessfulTestsCount">
            <summary>
            成功的测试数量
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.TestSuiteResult.FailedTestsCount">
            <summary>
            失败的测试数量
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.TestSuiteResult.TotalTestsCount">
            <summary>
            总测试数量
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.TestSuiteResult.SuccessRate">
            <summary>
            成功率
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.TestSuiteResult.ReportFilePath">
            <summary>
            报告文件路径
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.TestSuiteResult.SystemInfo">
            <summary>
            系统信息
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.TestSuiteResult.DriveInfo">
            <summary>
            驱动器信息
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.TestSuiteResult.ErrorMessage">
            <summary>
            套件级别的错误信息
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.TestSuiteResult.Warnings">
            <summary>
            套件级别的警告信息
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.TestSuiteResult.Logs">
            <summary>
            套件执行日志
            </summary>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Models.TestSuiteResult.AddTestResult(MassStorageStableTestTool.Core.Models.TestResult)">
            <summary>
            添加测试结果
            </summary>
            <param name="testResult">测试结果</param>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Models.TestSuiteResult.AddLog(System.String)">
            <summary>
            添加日志条目
            </summary>
            <param name="message">日志消息</param>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Models.TestSuiteResult.AddWarning(System.String)">
            <summary>
            添加警告
            </summary>
            <param name="warning">警告信息</param>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Models.TestSuiteResult.Start">
            <summary>
            开始测试套件
            </summary>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Models.TestSuiteResult.Complete(System.String)">
            <summary>
            完成测试套件
            </summary>
            <param name="errorMessage">错误信息（如果有）</param>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Models.TestSuiteResult.Cancel">
            <summary>
            取消测试套件
            </summary>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Models.TestSuiteResult.GetOverallPerformanceScore">
            <summary>
            获取综合性能评分
            </summary>
            <returns>综合评分</returns>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Models.TestSuiteResult.GetPerformanceSummary">
            <summary>
            获取性能摘要
            </summary>
            <returns>性能摘要字典</returns>
        </member>
        <member name="T:MassStorageStableTestTool.Core.Models.TestToolConfig">
            <summary>
            测试工具配置模型
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.TestToolConfig.Name">
            <summary>
            工具名称
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.TestToolConfig.Type">
            <summary>
            工具类型
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.TestToolConfig.ExecutablePath">
            <summary>
            可执行文件路径
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.TestToolConfig.WindowTitle">
            <summary>
            窗口标题（GUI工具使用）
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.TestToolConfig.DefaultParameters">
            <summary>
            默认参数
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.TestToolConfig.Timeouts">
            <summary>
            超时设置
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.TestToolConfig.Description">
            <summary>
            工具描述
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.TestToolConfig.Version">
            <summary>
            工具版本
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.TestToolConfig.Enabled">
            <summary>
            是否启用
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.TestToolConfig.Priority">
            <summary>
            优先级（数字越小优先级越高）
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.TestToolConfig.SupportedFileSystems">
            <summary>
            支持的文件系统类型
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.TestToolConfig.MinimumDiskSpace">
            <summary>
            最小磁盘空间要求（MB）
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.TestToolConfig.UIConfig">
            <summary>
            工具特定的UI配置（GUI工具使用）
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.TestToolConfig.CommandTemplate">
            <summary>
            命令行模板（CLI工具使用）
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.TestToolConfig.OutputParsing">
            <summary>
            输出解析配置
            </summary>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Models.TestToolConfig.Validate">
            <summary>
            验证工具是否可用
            </summary>
            <returns>验证结果</returns>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Models.TestToolConfig.GetParameter``1(System.String,``0)">
            <summary>
            获取参数值
            </summary>
            <typeparam name="T">参数类型</typeparam>
            <param name="key">参数键</param>
            <param name="defaultValue">默认值</param>
            <returns>参数值</returns>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Models.TestToolConfig.SetParameter(System.String,System.Object)">
            <summary>
            设置参数值
            </summary>
            <param name="key">参数键</param>
            <param name="value">参数值</param>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Models.TestToolConfig.Clone">
            <summary>
            克隆配置
            </summary>
            <returns>配置副本</returns>
        </member>
        <member name="T:MassStorageStableTestTool.Core.Models.TimeoutSettings">
            <summary>
            超时设置
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.TimeoutSettings.LaunchTimeout">
            <summary>
            启动超时时间
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.TimeoutSettings.TestTimeout">
            <summary>
            测试超时时间
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.TimeoutSettings.ShutdownTimeout">
            <summary>
            关闭超时时间
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.TimeoutSettings.ElementTimeout">
            <summary>
            UI元素查找超时时间
            </summary>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Models.TimeoutSettings.Clone">
            <summary>
            克隆超时设置
            </summary>
            <returns>超时设置副本</returns>
        </member>
        <member name="T:MassStorageStableTestTool.Core.Models.OutputParsingConfig">
            <summary>
            输出解析配置
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.OutputParsingConfig.Format">
            <summary>
            输出格式（如 "json", "text", "xml" 等）
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.OutputParsingConfig.ParsingRules">
            <summary>
            解析规则（正则表达式或XPath等）
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.OutputParsingConfig.Encoding">
            <summary>
            编码格式
            </summary>
        </member>
        <member name="P:MassStorageStableTestTool.Core.Models.OutputParsingConfig.IgnoreCase">
            <summary>
            是否忽略大小写
            </summary>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Models.OutputParsingConfig.Clone">
            <summary>
            克隆解析配置
            </summary>
            <returns>解析配置副本</returns>
        </member>
        <member name="T:MassStorageStableTestTool.Core.Services.ConfigurationService">
            <summary>
            配置服务实现
            </summary>
        </member>
        <member name="E:MassStorageStableTestTool.Core.Services.ConfigurationService.ConfigurationChanged">
            <summary>
            配置变化事件
            </summary>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Services.ConfigurationService.#ctor(System.String)">
            <summary>
            构造函数
            </summary>
            <param name="configPath">配置文件路径</param>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Services.ConfigurationService.LoadConfigurationAsync(System.String)">
            <summary>
            加载配置
            </summary>
            <param name="configPath">配置文件路径</param>
            <returns>配置对象</returns>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Services.ConfigurationService.SaveConfigurationAsync(MassStorageStableTestTool.Core.Models.ApplicationConfiguration,System.String)">
            <summary>
            保存配置
            </summary>
            <param name="configuration">配置对象</param>
            <param name="configPath">配置文件路径</param>
            <returns>保存结果</returns>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Services.ConfigurationService.CreateDefaultConfiguration">
            <summary>
            创建默认配置
            </summary>
            <returns>默认配置</returns>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Services.ConfigurationService.CreateDefaultTestToolConfigurations">
            <summary>
            创建默认测试工具配置
            </summary>
            <returns>测试工具配置字典</returns>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Services.ConfigurationService.ValidateConfiguration(MassStorageStableTestTool.Core.Models.ApplicationConfiguration)">
            <summary>
            验证配置
            </summary>
            <param name="configuration">配置对象</param>
            <returns>验证结果</returns>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Services.ConfigurationService.GetToolConfigurationAsync(System.String)">
            <summary>
            获取测试工具配置
            </summary>
            <param name="toolName">工具名称</param>
            <returns>工具配置</returns>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Services.ConfigurationService.UpdateToolConfigurationAsync(System.String,MassStorageStableTestTool.Core.Models.TestToolConfig)">
            <summary>
            更新测试工具配置
            </summary>
            <param name="toolName">工具名称</param>
            <param name="config">工具配置</param>
            <returns>更新结果</returns>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Services.ConfigurationService.GetAllToolConfigurationsAsync">
            <summary>
            获取所有测试工具配置
            </summary>
            <returns>工具配置字典</returns>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Services.ConfigurationService.ImportConfigurationAsync(System.String,System.String)">
            <summary>
            导入配置
            </summary>
            <param name="filePath">配置文件路径</param>
            <param name="format">配置格式</param>
            <returns>导入的配置</returns>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Services.ConfigurationService.ExportConfigurationAsync(MassStorageStableTestTool.Core.Models.ApplicationConfiguration,System.String,System.String)">
            <summary>
            导出配置
            </summary>
            <param name="configuration">配置对象</param>
            <param name="filePath">导出文件路径</param>
            <param name="format">导出格式</param>
            <returns>导出结果</returns>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Services.ConfigurationService.ResetToDefaultAsync">
            <summary>
            重置配置为默认值
            </summary>
            <returns>重置结果</returns>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Services.ConfigurationService.OnConfigurationChanged(MassStorageStableTestTool.Core.Interfaces.ConfigurationChangeType,System.String,System.Object,System.Object)">
            <summary>
            触发配置变化事件
            </summary>
            <param name="changeType">变化类型</param>
            <param name="configKey">配置键</param>
            <param name="oldValue">旧值</param>
            <param name="newValue">新值</param>
        </member>
        <member name="T:MassStorageStableTestTool.Core.Services.ReportService">
            <summary>
            报告服务实现
            </summary>
        </member>
        <member name="E:MassStorageStableTestTool.Core.Services.ReportService.ReportGenerated">
            <summary>
            报告生成完成事件
            </summary>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Services.ReportService.#ctor(System.String)">
            <summary>
            构造函数
            </summary>
            <param name="outputDirectory">输出目录</param>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Services.ReportService.GenerateReportAsync(MassStorageStableTestTool.Core.Models.TestSuiteResult,MassStorageStableTestTool.Core.Enums.ReportFormat)">
            <summary>
            生成测试报告
            </summary>
            <param name="testSuiteResult">测试套件结果</param>
            <param name="format">报告格式</param>
            <returns>报告内容</returns>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Services.ReportService.GenerateAndSaveReportAsync(MassStorageStableTestTool.Core.Models.TestSuiteResult,System.String,MassStorageStableTestTool.Core.Enums.ReportFormat)">
            <summary>
            生成并保存报告
            </summary>
            <param name="testSuiteResult">测试套件结果</param>
            <param name="outputPath">输出路径</param>
            <param name="format">报告格式</param>
            <returns>保存的文件路径</returns>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Services.ReportService.ExportReportAsync(System.String,System.String,MassStorageStableTestTool.Core.Enums.ReportFormat)">
            <summary>
            导出报告到文件
            </summary>
            <param name="reportContent">报告内容</param>
            <param name="filePath">文件路径</param>
            <param name="format">报告格式</param>
            <returns>导出结果</returns>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Services.ReportService.GenerateTextReportAsync(MassStorageStableTestTool.Core.Models.TestSuiteResult)">
            <summary>
            生成文本格式报告
            </summary>
            <param name="testSuiteResult">测试套件结果</param>
            <returns>文本报告</returns>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Services.ReportService.GenerateJsonReportAsync(MassStorageStableTestTool.Core.Models.TestSuiteResult)">
            <summary>
            生成JSON格式报告
            </summary>
            <param name="testSuiteResult">测试套件结果</param>
            <returns>JSON报告</returns>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Services.ReportService.GenerateCsvReportAsync(MassStorageStableTestTool.Core.Models.TestSuiteResult)">
            <summary>
            生成CSV格式报告
            </summary>
            <param name="testSuiteResult">测试套件结果</param>
            <returns>CSV报告</returns>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Services.ReportService.GenerateHtmlReportAsync(MassStorageStableTestTool.Core.Models.TestSuiteResult)">
            <summary>
            生成HTML格式报告
            </summary>
            <param name="testSuiteResult">测试套件结果</param>
            <returns>HTML报告</returns>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Services.ReportService.GenerateXmlReportAsync(MassStorageStableTestTool.Core.Models.TestSuiteResult)">
            <summary>
            生成XML格式报告
            </summary>
            <param name="testSuiteResult">测试套件结果</param>
            <returns>XML报告</returns>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Services.ReportService.GenerateDefaultFileName(MassStorageStableTestTool.Core.Models.TestSuiteResult,MassStorageStableTestTool.Core.Enums.ReportFormat)">
            <summary>
            生成默认文件名
            </summary>
            <param name="testSuiteResult">测试套件结果</param>
            <param name="format">报告格式</param>
            <returns>文件路径</returns>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Services.ReportService.EscapeCsvField(System.String)">
            <summary>
            转义CSV字段
            </summary>
            <param name="field">字段值</param>
            <returns>转义后的字段</returns>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Services.ReportService.OnReportGenerated(System.String,MassStorageStableTestTool.Core.Enums.ReportFormat,System.Int64,System.TimeSpan,System.Boolean,System.String)">
            <summary>
            触发报告生成事件
            </summary>
            <param name="filePath">文件路径</param>
            <param name="format">报告格式</param>
            <param name="size">文件大小</param>
            <param name="duration">生成耗时</param>
            <param name="isSuccess">是否成功</param>
            <param name="errorMessage">错误信息</param>
        </member>
        <member name="T:MassStorageStableTestTool.Core.Services.SystemInfoService">
            <summary>
            系统信息服务实现
            </summary>
        </member>
        <member name="E:MassStorageStableTestTool.Core.Services.SystemInfoService.PerformanceDataUpdated">
            <summary>
            性能数据更新事件
            </summary>
        </member>
        <member name="E:MassStorageStableTestTool.Core.Services.SystemInfoService.DriveStatusChanged">
            <summary>
            驱动器状态变化事件
            </summary>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Services.SystemInfoService.GetSystemInfoAsync">
            <summary>
            获取系统信息
            </summary>
            <returns>系统信息</returns>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Services.SystemInfoService.GetWindowsSpecificInfoAsync(MassStorageStableTestTool.Core.Models.SystemInfo)">
            <summary>
            获取Windows特定信息
            </summary>
            <param name="systemInfo">系统信息对象</param>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Services.SystemInfoService.GetDriveInfoAsync(System.String)">
            <summary>
            获取驱动器信息
            </summary>
            <param name="driveName">驱动器名称</param>
            <returns>驱动器信息</returns>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Services.SystemInfoService.GetWindowsDriveInfoAsync(MassStorageStableTestTool.Core.Models.DriveInfo,System.String)">
            <summary>
            获取Windows驱动器详细信息
            </summary>
            <param name="driveInfo">驱动器信息对象</param>
            <param name="driveName">驱动器名称</param>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Services.SystemInfoService.GetAvailableDrivesAsync">
            <summary>
            获取所有可用驱动器
            </summary>
            <returns>驱动器信息列表</returns>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Services.SystemInfoService.GetRemovableDrivesAsync">
            <summary>
            获取可移动驱动器列表
            </summary>
            <returns>可移动驱动器列表</returns>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Services.SystemInfoService.CheckDriveSuitabilityAsync(System.String,System.Int64)">
            <summary>
            检查驱动器是否适合测试
            </summary>
            <param name="driveName">驱动器名称</param>
            <param name="requiredSpace">所需空间（字节）</param>
            <returns>检查结果</returns>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Services.SystemInfoService.GetSystemPerformanceAsync">
            <summary>
            获取系统性能信息
            </summary>
            <returns>性能信息</returns>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Services.SystemInfoService.GetWindowsPerformanceInfoAsync(MassStorageStableTestTool.Core.Interfaces.SystemPerformanceInfo)">
            <summary>
            获取Windows性能信息
            </summary>
            <param name="performanceInfo">性能信息对象</param>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Services.SystemInfoService.StartPerformanceMonitoringAsync(System.TimeSpan,System.Threading.CancellationToken)">
            <summary>
            监控系统性能
            </summary>
            <param name="interval">监控间隔</param>
            <param name="cancellationToken">取消令牌</param>
            <returns>性能监控任务</returns>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Services.SystemInfoService.StopPerformanceMonitoringAsync">
            <summary>
            停止性能监控
            </summary>
            <returns>停止结果</returns>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Services.SystemInfoService.GetInstalledSoftwareAsync">
            <summary>
            获取已安装的软件列表
            </summary>
            <returns>已安装软件列表</returns>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Services.SystemInfoService.IsSoftwareInstalledAsync(System.String)">
            <summary>
            检查软件是否已安装
            </summary>
            <param name="softwareName">软件名称</param>
            <returns>是否已安装</returns>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Services.SystemInfoService.GetEnvironmentVariablesAsync">
            <summary>
            获取环境变量
            </summary>
            <returns>环境变量字典</returns>
        </member>
        <member name="M:MassStorageStableTestTool.Core.Services.SystemInfoService.GetNetworkAdaptersAsync">
            <summary>
            获取网络适配器信息
            </summary>
            <returns>网络适配器列表</returns>
        </member>
    </members>
</doc>
