using MassStorageStableTestTool.Core.Enums;
using MassStorageStableTestTool.Core.Models;

namespace MassStorageStableTestTool.Core.Interfaces;

/// <summary>
/// 测试工具控制器接口
/// </summary>
public interface ITestToolController
{
    /// <summary>
    /// 工具名称
    /// </summary>
    string ToolName { get; }

    /// <summary>
    /// 工具类型
    /// </summary>
    TestToolType ToolType { get; }

    /// <summary>
    /// 工具配置
    /// </summary>
    TestToolConfig Configuration { get; }

    /// <summary>
    /// 执行测试
    /// </summary>
    /// <param name="config">测试配置</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <param name="progress">进度报告器</param>
    /// <returns>测试结果</returns>
    Task<TestResult> ExecuteTestAsync(
        TestConfiguration config, 
        CancellationToken cancellationToken,
        IProgress<ProgressEventArgs>? progress = null);

    /// <summary>
    /// 检查工具是否可用
    /// </summary>
    /// <returns>是否可用</returns>
    bool IsToolAvailable();

    /// <summary>
    /// 获取工具版本
    /// </summary>
    /// <returns>工具版本</returns>
    Task<string> GetToolVersionAsync();

    /// <summary>
    /// 验证测试配置
    /// </summary>
    /// <param name="config">测试配置</param>
    /// <returns>验证结果</returns>
    (bool IsValid, List<string> Errors) ValidateConfiguration(TestConfiguration config);

    /// <summary>
    /// 准备测试环境
    /// </summary>
    /// <param name="config">测试配置</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>准备结果</returns>
    Task<bool> PrepareTestEnvironmentAsync(TestConfiguration config, CancellationToken cancellationToken);

    /// <summary>
    /// 清理测试环境
    /// </summary>
    /// <param name="config">测试配置</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>清理结果</returns>
    Task<bool> CleanupTestEnvironmentAsync(TestConfiguration config, CancellationToken cancellationToken);

    /// <summary>
    /// 停止正在运行的测试
    /// </summary>
    /// <returns>停止结果</returns>
    Task<bool> StopTestAsync();

    /// <summary>
    /// 获取当前测试状态
    /// </summary>
    /// <returns>测试状态</returns>
    TestStatus GetCurrentStatus();

    /// <summary>
    /// 获取支持的参数列表
    /// </summary>
    /// <returns>支持的参数</returns>
    Dictionary<string, ParameterInfo> GetSupportedParameters();

    /// <summary>
    /// 状态变化事件
    /// </summary>
    event EventHandler<TestStatusChangedEventArgs>? StatusChanged;

    /// <summary>
    /// 日志事件
    /// </summary>
    event EventHandler<LogEventArgs>? LogReceived;
}

/// <summary>
/// 进度事件参数
/// </summary>
public class ProgressEventArgs : EventArgs
{
    /// <summary>
    /// 进度百分比 (0-100)
    /// </summary>
    public double Progress { get; set; }

    /// <summary>
    /// 状态消息
    /// </summary>
    public string Status { get; set; } = string.Empty;

    /// <summary>
    /// 当前阶段
    /// </summary>
    public string Phase { get; set; } = string.Empty;

    /// <summary>
    /// 详细信息
    /// </summary>
    public Dictionary<string, object> Details { get; set; } = new();

    /// <summary>
    /// 时间戳
    /// </summary>
    public DateTime Timestamp { get; set; } = DateTime.Now;
}

/// <summary>
/// 测试状态变化事件参数
/// </summary>
public class TestStatusChangedEventArgs : EventArgs
{
    /// <summary>
    /// 工具名称
    /// </summary>
    public string ToolName { get; set; } = string.Empty;

    /// <summary>
    /// 旧状态
    /// </summary>
    public TestStatus OldStatus { get; set; }

    /// <summary>
    /// 新状态
    /// </summary>
    public TestStatus NewStatus { get; set; }

    /// <summary>
    /// 状态消息
    /// </summary>
    public string? Message { get; set; }

    /// <summary>
    /// 时间戳
    /// </summary>
    public DateTime Timestamp { get; set; } = DateTime.Now;
}

/// <summary>
/// 日志事件参数
/// </summary>
public class LogEventArgs : EventArgs
{
    /// <summary>
    /// 日志级别
    /// </summary>
    public LogLevel Level { get; set; }

    /// <summary>
    /// 日志消息
    /// </summary>
    public string Message { get; set; } = string.Empty;

    /// <summary>
    /// 异常信息
    /// </summary>
    public Exception? Exception { get; set; }

    /// <summary>
    /// 时间戳
    /// </summary>
    public DateTime Timestamp { get; set; } = DateTime.Now;

    /// <summary>
    /// 日志来源
    /// </summary>
    public string Source { get; set; } = string.Empty;
}

/// <summary>
/// 参数信息
/// </summary>
public class ParameterInfo
{
    /// <summary>
    /// 参数名称
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 参数类型
    /// </summary>
    public Type Type { get; set; } = typeof(string);

    /// <summary>
    /// 默认值
    /// </summary>
    public object? DefaultValue { get; set; }

    /// <summary>
    /// 参数描述
    /// </summary>
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// 是否必需
    /// </summary>
    public bool IsRequired { get; set; }

    /// <summary>
    /// 可选值列表
    /// </summary>
    public List<object>? PossibleValues { get; set; }

    /// <summary>
    /// 最小值
    /// </summary>
    public object? MinValue { get; set; }

    /// <summary>
    /// 最大值
    /// </summary>
    public object? MaxValue { get; set; }

    /// <summary>
    /// 验证规则（正则表达式）
    /// </summary>
    public string? ValidationPattern { get; set; }
}

/// <summary>
/// 日志级别枚举
/// </summary>
public enum LogLevel
{
    /// <summary>
    /// 调试
    /// </summary>
    Debug,

    /// <summary>
    /// 信息
    /// </summary>
    Information,

    /// <summary>
    /// 警告
    /// </summary>
    Warning,

    /// <summary>
    /// 错误
    /// </summary>
    Error,

    /// <summary>
    /// 严重错误
    /// </summary>
    Critical
}
