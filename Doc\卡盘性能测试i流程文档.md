# 卡盘性能测试流程步骤说明
## 性能测试流程简介
![alt text](image.png)

```mermaid
flowchart TD
A([start])-->B([H2 Pass 样品])
B-->C[选择测试集合]
C-->D[配置每个测试项的参数]
D-->E[格式化磁盘]
E-->F[执行测试项]
F-->H[保存测试结果]
H-->G{测试是否成功?}
G-->|是|I{是否还有测试项?}
G-->|否|L[输出测试日志]
I-->|否|L
I-->|是|E
L-->J([end])
```


## 一、H2 Speed测试
1) 每个HUB口插1pc，每台电脑4pcs，
2) 自动识别盘符是否为空盘，测试前自动删除盘内数据，保证测试盘为空盘。
3) 以管理员方式打开H2测试软件,选择‘手动’，打开设置，选择所有空间，然后点击确定。
4) 样片上盘，点击“全部开始”运行测试。
5) 测试完成后进行截图保存，命名规则：测试PC编号+测试项目名称，如：PC-01 H2 Speed.
6) 输出简要测试报告，包括最终测试结果和测试截图即可。

![alt text](image-5.png)
 
## 二、CrystalDiskMark（CDM）
1) 每个HUB口插1pc，每台电脑4pcs，
2) 自动识别盘符是否为空盘，测试前自动删除盘内数据，保证测试盘为空盘。
3) 打开CrystalDiskMark8.0.1→选Disk Mark64（运行次数选5，测试数据大小1GiB，测试驱动器选对应的盘）→确认后点击ALL开始
4) 跑完后截图保存到对应文件夹里，命名规则：测试PC编号+测试项目名称，如：PC-01 CDM.
5) 输出简要测试报告，包括最终测试结果和测试截图即可。
6) 测试软件设置如下：

![alt text](image-2.png)

## 三、HD Bench 340（HD）
1) 每个HUB口插1pc，每台电脑4pcs，
2) 自动识别盘符是否为空盘，测试前自动删除盘内数据，保证测试盘为空盘。
3) 在电脑桌面测试软件文件夹里打开HD Bench 340→HDBENCH（右下角选对应的磁盘，下列写1000MB）→点击左下灰色箱子开始
4) 跑完截图保存到对应文件夹里，命名规则：测试PC编号+测试项目名称，如：PC-01 HD.
5) 输出简要测试报告，包括最终测试结果和测试截图即可。
6) 测试软件设置如下：

![alt text](image-3.png)

 

## 四、ATTO
1) 每个HUB口插1pc，每台电脑4pcs，
2) 自动识别盘符是否为空盘，测试前自动删除盘内数据，保证测试盘为空盘。
3) 在电脑桌面测试软件文件夹里管理员身份打开ATTO应用程序→选对应的磁盘→Total Length选256MB→点击Star开始
4) 跑完截图保存到对应文件夹里，命名规则：测试PC编号+测试项目名称，如：PC-01 ATTO.
5) 输出简要测试报告，包括最终测试结果和测试截图即可。
6) 测试软件设置如下：
    ![alt text](image-4.png)
 

## 五、HDtunepro5.75（HDTP）
1) 每个HUB口插1pc，每台电脑4pcs，
2) 自动识别盘符是否为空盘，测试前自动删除盘内数据，保证测试盘为空盘。
3) 在电脑桌面测试软件文件夹里打开HD TunePro5.75应用程序（选盘符）→点击开始（文件基准、基准、随机访问都需要测试，读取测试完成后需要截图保存，规则：测试PC+样品编号+容量模式+R）
4) 跑完截图到对应文件夹里的HDTP文件夹
5) 完右键此电脑点击管理，在存储磁盘管理里找对应的盘右键删除卷，弹出的两个窗口都点击 是，关闭
6) 回HD Tunepro5.75，右边选写入再点击开始，弹出的窗口要勾选"运行写入测试"，点击确定
7) 跑完截图到对应文件夹里的HDTP文件夹, 命名规则：测试PC+样品编号+容量模式+R
8) 右键此电脑管理，到存储磁盘管理里找对应的盘，右键新建简单卷，弹出的三个窗口都点击下一步，第四个窗口的文件系统64G 选默认  而32G的选FAT32，标卷的新建卷要删除，然后下一步完成。
9) 输出简要测试报告，包括最终测试结果和测试截图即可。
10) 测试软件设置如下：

 ![alt text](image-1.png)
 ![alt text](image-6.png)

 
