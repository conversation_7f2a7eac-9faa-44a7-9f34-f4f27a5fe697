using MassStorageStableTestTool.Core.Interfaces;
using MassStorageStableTestTool.Core.Models;
using MassStorageStableTestTool.Core.Enums;
using System.Text;
using System.Text.Json;

namespace MassStorageStableTestTool.Core.Services;

/// <summary>
/// 报告服务实现
/// </summary>
public class ReportService : IReportService
{
    private readonly string _defaultOutputDirectory;

    /// <summary>
    /// 报告生成完成事件
    /// </summary>
    public event EventHandler<ReportGeneratedEventArgs>? ReportGenerated;

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="outputDirectory">输出目录</param>
    public ReportService(string? outputDirectory = null)
    {
        _defaultOutputDirectory = outputDirectory ?? Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Reports");
    }

    /// <summary>
    /// 生成测试报告
    /// </summary>
    /// <param name="testSuiteResult">测试套件结果</param>
    /// <param name="format">报告格式</param>
    /// <returns>报告内容</returns>
    public async Task<string> GenerateReportAsync(TestSuiteResult testSuiteResult, ReportFormat format = ReportFormat.Text)
    {
        if (testSuiteResult == null)
        {
            throw new ArgumentNullException(nameof(testSuiteResult));
        }

        var startTime = DateTime.Now;
        string reportContent;

        try
        {
            reportContent = format switch
            {
                ReportFormat.Text => await GenerateTextReportAsync(testSuiteResult),
                ReportFormat.JSON => await GenerateJsonReportAsync(testSuiteResult),
                ReportFormat.CSV => await GenerateCsvReportAsync(testSuiteResult),
                ReportFormat.HTML => await GenerateHtmlReportAsync(testSuiteResult),
                ReportFormat.XML => await GenerateXmlReportAsync(testSuiteResult),
                _ => await GenerateTextReportAsync(testSuiteResult)
            };

            var duration = DateTime.Now - startTime;
            OnReportGenerated(string.Empty, format, reportContent.Length, duration, true);

            return reportContent;
        }
        catch (Exception ex)
        {
            var duration = DateTime.Now - startTime;
            OnReportGenerated(string.Empty, format, 0, duration, false, ex.Message);
            throw;
        }
    }

    /// <summary>
    /// 生成并保存报告
    /// </summary>
    /// <param name="testSuiteResult">测试套件结果</param>
    /// <param name="outputPath">输出路径</param>
    /// <param name="format">报告格式</param>
    /// <returns>保存的文件路径</returns>
    public async Task<string> GenerateAndSaveReportAsync(TestSuiteResult testSuiteResult, string? outputPath = null, ReportFormat format = ReportFormat.Text)
    {
        var reportContent = await GenerateReportAsync(testSuiteResult, format);
        
        if (string.IsNullOrEmpty(outputPath))
        {
            outputPath = GenerateDefaultFileName(testSuiteResult, format);
        }

        await ExportReportAsync(reportContent, outputPath, format);
        return outputPath;
    }

    /// <summary>
    /// 导出报告到文件
    /// </summary>
    /// <param name="reportContent">报告内容</param>
    /// <param name="filePath">文件路径</param>
    /// <param name="format">报告格式</param>
    /// <returns>导出结果</returns>
    public async Task<bool> ExportReportAsync(string reportContent, string filePath, ReportFormat format)
    {
        try
        {
            // 确保目录存在
            var directory = Path.GetDirectoryName(filePath);
            if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
            {
                Directory.CreateDirectory(directory);
            }

            await File.WriteAllTextAsync(filePath, reportContent, Encoding.UTF8);
            return true;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"导出报告失败: {ex.Message}");
            return false;
        }
    }

    /// <summary>
    /// 生成文本格式报告
    /// </summary>
    /// <param name="testSuiteResult">测试套件结果</param>
    /// <returns>文本报告</returns>
    private async Task<string> GenerateTextReportAsync(TestSuiteResult testSuiteResult)
    {
        var report = new StringBuilder();

        // 报告头部
        report.AppendLine("===============================================");
        report.AppendLine("SD卡稳定性测试报告");
        report.AppendLine("===============================================");
        report.AppendLine();

        // 测试信息
        report.AppendLine("测试信息:");
        report.AppendLine($"- 测试日期: {testSuiteResult.StartTime:yyyy-MM-dd HH:mm:ss}");
        report.AppendLine($"- 目标驱动器: {testSuiteResult.Configuration.TargetDrive}");
        report.AppendLine($"- 测试工具: {string.Join(", ", testSuiteResult.Configuration.SelectedTools)}");
        report.AppendLine($"- 测试时长: {testSuiteResult.Duration.TotalMinutes:F1} 分钟");
        report.AppendLine();

        // 系统信息
        if (testSuiteResult.SystemInfo != null)
        {
            report.AppendLine("系统信息:");
            report.AppendLine($"- 操作系统: {testSuiteResult.SystemInfo.OperatingSystem} {testSuiteResult.SystemInfo.OSVersion}");
            report.AppendLine($"- 处理器: {testSuiteResult.SystemInfo.Processor}");
            report.AppendLine($"- 内存: {testSuiteResult.SystemInfo.TotalMemory:F1} GB");
            report.AppendLine();
        }

        // 驱动器信息
        if (testSuiteResult.DriveInfo != null)
        {
            report.AppendLine("驱动器信息:");
            report.AppendLine($"- 名称: {testSuiteResult.DriveInfo.Name}");
            report.AppendLine($"- 标签: {testSuiteResult.DriveInfo.Label}");
            report.AppendLine($"- 文件系统: {testSuiteResult.DriveInfo.FileSystem}");
            report.AppendLine($"- 总容量: {testSuiteResult.DriveInfo.GetFormattedTotalSize()}");
            report.AppendLine($"- 可用空间: {testSuiteResult.DriveInfo.GetFormattedFreeSpace()}");
            report.AppendLine();
        }

        // 测试结果汇总
        report.AppendLine("===============================================");
        report.AppendLine("测试结果汇总");
        report.AppendLine("===============================================");
        report.AppendLine();
        report.AppendLine($"总体状态: {(testSuiteResult.AllTestsPassed ? "通过" : "失败")}");
        report.AppendLine($"成功测试: {testSuiteResult.SuccessfulTestsCount}/{testSuiteResult.TotalTestsCount}");
        report.AppendLine($"成功率: {testSuiteResult.SuccessRate:F1}%");
        report.AppendLine();

        // 详细测试结果
        foreach (var testResult in testSuiteResult.TestResults)
        {
            report.AppendLine($"--- {testResult.ToolName} ---");
            report.AppendLine($"状态: {(testResult.Success ? "通过" : "失败")}");
            report.AppendLine($"开始时间: {testResult.StartTime:yyyy-MM-dd HH:mm:ss}");
            report.AppendLine($"结束时间: {testResult.EndTime:yyyy-MM-dd HH:mm:ss}");
            report.AppendLine($"耗时: {testResult.Duration.TotalMinutes:F1} 分钟");

            if (testResult.Success && testResult.Performance != null)
            {
                var perf = testResult.Performance;
                if (perf.ReadSpeed.HasValue)
                    report.AppendLine($"读取速度: {perf.ReadSpeed.Value:F2} MB/s");
                if (perf.WriteSpeed.HasValue)
                    report.AppendLine($"写入速度: {perf.WriteSpeed.Value:F2} MB/s");
                if (perf.TotalIOPS.HasValue)
                    report.AppendLine($"总IOPS: {perf.TotalIOPS.Value:F0}");
                if (perf.AverageLatency.HasValue)
                    report.AppendLine($"平均延迟: {perf.AverageLatency.Value:F2} ms");
                
                report.AppendLine($"性能评分: {perf.GetOverallScore():F1}/100");
                report.AppendLine($"性能等级: {perf.GetPerformanceGrade()}");
            }
            else if (!testResult.Success)
            {
                report.AppendLine($"错误信息: {testResult.ErrorMessage}");
            }

            if (testResult.Warnings.Any())
            {
                report.AppendLine("警告:");
                foreach (var warning in testResult.Warnings)
                {
                    report.AppendLine($"  - {warning}");
                }
            }

            report.AppendLine();
        }

        // 性能摘要
        var performanceSummary = testSuiteResult.GetPerformanceSummary();
        if (performanceSummary.Any())
        {
            report.AppendLine("===============================================");
            report.AppendLine("性能摘要");
            report.AppendLine("===============================================");
            
            foreach (var kvp in performanceSummary)
            {
                report.AppendLine($"{kvp.Key}: {kvp.Value}");
            }
            report.AppendLine();
        }

        // 报告尾部
        report.AppendLine("===============================================");
        report.AppendLine($"报告生成时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
        report.AppendLine("由 MassStorageStableTestTool 生成");
        report.AppendLine("===============================================");

        return report.ToString();
    }

    /// <summary>
    /// 生成JSON格式报告
    /// </summary>
    /// <param name="testSuiteResult">测试套件结果</param>
    /// <returns>JSON报告</returns>
    private async Task<string> GenerateJsonReportAsync(TestSuiteResult testSuiteResult)
    {
        var reportData = new
        {
            ReportInfo = new
            {
                GeneratedAt = DateTime.Now,
                Version = "1.0",
                Tool = "MassStorageStableTestTool"
            },
            TestSuite = new
            {
                testSuiteResult.StartTime,
                testSuiteResult.EndTime,
                Duration = testSuiteResult.Duration.TotalSeconds,
                testSuiteResult.Status,
                testSuiteResult.AllTestsPassed,
                testSuiteResult.SuccessfulTestsCount,
                testSuiteResult.FailedTestsCount,
                testSuiteResult.TotalTestsCount,
                testSuiteResult.SuccessRate
            },
            Configuration = testSuiteResult.Configuration,
            SystemInfo = testSuiteResult.SystemInfo,
            DriveInfo = testSuiteResult.DriveInfo,
            TestResults = testSuiteResult.TestResults.Select(r => new
            {
                r.ToolName,
                r.Success,
                r.Status,
                r.StartTime,
                r.EndTime,
                Duration = r.Duration.TotalSeconds,
                r.Data,
                r.ErrorMessage,
                r.Warnings,
                Performance = r.Performance?.ToDictionary()
            }),
            PerformanceSummary = testSuiteResult.GetPerformanceSummary()
        };

        var options = new JsonSerializerOptions
        {
            WriteIndented = true,
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase
        };

        return JsonSerializer.Serialize(reportData, options);
    }

    /// <summary>
    /// 生成CSV格式报告
    /// </summary>
    /// <param name="testSuiteResult">测试套件结果</param>
    /// <returns>CSV报告</returns>
    private async Task<string> GenerateCsvReportAsync(TestSuiteResult testSuiteResult)
    {
        var csv = new StringBuilder();

        // CSV头部
        csv.AppendLine("工具名称,状态,开始时间,结束时间,耗时(分钟),读取速度(MB/s),写入速度(MB/s),总IOPS,平均延迟(ms),性能评分,错误信息");

        // 测试结果数据
        foreach (var result in testSuiteResult.TestResults)
        {
            var line = new List<string>
            {
                EscapeCsvField(result.ToolName),
                result.Success ? "成功" : "失败",
                result.StartTime.ToString("yyyy-MM-dd HH:mm:ss"),
                result.EndTime.ToString("yyyy-MM-dd HH:mm:ss"),
                result.Duration.TotalMinutes.ToString("F1"),
                result.Performance?.ReadSpeed?.ToString("F2") ?? "",
                result.Performance?.WriteSpeed?.ToString("F2") ?? "",
                result.Performance?.TotalIOPS?.ToString("F0") ?? "",
                result.Performance?.AverageLatency?.ToString("F2") ?? "",
                result.Performance?.GetOverallScore().ToString("F1") ?? "",
                EscapeCsvField(result.ErrorMessage ?? "")
            };

            csv.AppendLine(string.Join(",", line));
        }

        return csv.ToString();
    }

    /// <summary>
    /// 生成HTML格式报告
    /// </summary>
    /// <param name="testSuiteResult">测试套件结果</param>
    /// <returns>HTML报告</returns>
    private async Task<string> GenerateHtmlReportAsync(TestSuiteResult testSuiteResult)
    {
        // 简化的HTML报告实现
        var html = new StringBuilder();
        html.AppendLine("<!DOCTYPE html>");
        html.AppendLine("<html><head><title>SD卡稳定性测试报告</title></head>");
        html.AppendLine("<body>");
        html.AppendLine("<h1>SD卡稳定性测试报告</h1>");
        
        // 将文本报告转换为HTML
        var textReport = await GenerateTextReportAsync(testSuiteResult);
        html.AppendLine("<pre>");
        html.AppendLine(System.Web.HttpUtility.HtmlEncode(textReport));
        html.AppendLine("</pre>");
        
        html.AppendLine("</body></html>");
        return html.ToString();
    }

    /// <summary>
    /// 生成XML格式报告
    /// </summary>
    /// <param name="testSuiteResult">测试套件结果</param>
    /// <returns>XML报告</returns>
    private async Task<string> GenerateXmlReportAsync(TestSuiteResult testSuiteResult)
    {
        // 简化的XML报告实现
        var xml = new StringBuilder();
        xml.AppendLine("<?xml version=\"1.0\" encoding=\"UTF-8\"?>");
        xml.AppendLine("<TestReport>");
        xml.AppendLine($"  <GeneratedAt>{DateTime.Now:yyyy-MM-dd HH:mm:ss}</GeneratedAt>");
        xml.AppendLine($"  <OverallStatus>{(testSuiteResult.AllTestsPassed ? "Pass" : "Fail")}</OverallStatus>");
        xml.AppendLine($"  <SuccessRate>{testSuiteResult.SuccessRate:F1}</SuccessRate>");
        xml.AppendLine("  <TestResults>");
        
        foreach (var result in testSuiteResult.TestResults)
        {
            xml.AppendLine("    <TestResult>");
            xml.AppendLine($"      <ToolName>{System.Web.HttpUtility.HtmlEncode(result.ToolName)}</ToolName>");
            xml.AppendLine($"      <Status>{(result.Success ? "Pass" : "Fail")}</Status>");
            xml.AppendLine($"      <Duration>{result.Duration.TotalMinutes:F1}</Duration>");
            xml.AppendLine("    </TestResult>");
        }
        
        xml.AppendLine("  </TestResults>");
        xml.AppendLine("</TestReport>");
        return xml.ToString();
    }

    /// <summary>
    /// 生成默认文件名
    /// </summary>
    /// <param name="testSuiteResult">测试套件结果</param>
    /// <param name="format">报告格式</param>
    /// <returns>文件路径</returns>
    private string GenerateDefaultFileName(TestSuiteResult testSuiteResult, ReportFormat format)
    {
        var driveLabel = testSuiteResult.DriveInfo?.Label ?? "Unknown";
        var timestamp = testSuiteResult.StartTime.ToString("yyyy-MM-dd_HH-mm-ss");
        var extension = format.ToString().ToLowerInvariant();
        
        var fileName = $"{driveLabel}_{timestamp}_Report.{extension}";
        return Path.Combine(_defaultOutputDirectory, fileName);
    }

    /// <summary>
    /// 转义CSV字段
    /// </summary>
    /// <param name="field">字段值</param>
    /// <returns>转义后的字段</returns>
    private string EscapeCsvField(string field)
    {
        if (string.IsNullOrEmpty(field))
            return "";

        if (field.Contains(',') || field.Contains('"') || field.Contains('\n'))
        {
            return $"\"{field.Replace("\"", "\"\"")}\"";
        }

        return field;
    }

    /// <summary>
    /// 触发报告生成事件
    /// </summary>
    /// <param name="filePath">文件路径</param>
    /// <param name="format">报告格式</param>
    /// <param name="size">文件大小</param>
    /// <param name="duration">生成耗时</param>
    /// <param name="isSuccess">是否成功</param>
    /// <param name="errorMessage">错误信息</param>
    private void OnReportGenerated(string filePath, ReportFormat format, long size, TimeSpan duration, bool isSuccess, string? errorMessage = null)
    {
        ReportGenerated?.Invoke(this, new ReportGeneratedEventArgs
        {
            FilePath = filePath,
            Format = format,
            Size = size,
            Duration = duration,
            IsSuccess = isSuccess,
            ErrorMessage = errorMessage,
            Timestamp = DateTime.Now
        });
    }

    // 其他接口方法的简化实现
    public async Task<string> GetReportTemplateAsync(string templateName) => string.Empty;
    public async Task<bool> SetReportTemplateAsync(string templateName, string templateContent) => false;
    public async Task<List<string>> GetAvailableTemplatesAsync() => new();
    public async Task<string> GenerateSummaryReportAsync(TestSuiteResult testSuiteResult) => await GenerateTextReportAsync(testSuiteResult);
    public async Task<string> GenerateDetailedReportAsync(TestSuiteResult testSuiteResult) => await GenerateTextReportAsync(testSuiteResult);
    public async Task<string> GeneratePerformanceReportAsync(TestSuiteResult testSuiteResult) => await GenerateTextReportAsync(testSuiteResult);
    public async Task<string> GenerateComparisonReportAsync(List<TestSuiteResult> testResults) => string.Empty;
    public async Task<bool> SendReportEmailAsync(string reportContent, List<string> recipients, string subject) => false;
    public async Task<List<ReportHistory>> GetReportHistoryAsync(int count = 10) => new();
    public async Task<bool> DeleteReportAsync(string filePath) => false;
    public async Task<int> CleanupExpiredReportsAsync(int retentionDays = 30) => 0;
    public (bool IsValid, List<string> Errors) ValidateReport(string reportContent, ReportFormat format) => (true, new());
}
